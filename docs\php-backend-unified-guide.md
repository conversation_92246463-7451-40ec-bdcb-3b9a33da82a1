# PHP 后端统一配置指南

## 概述

本文档涵盖了三个核心转换方法的 PHP 后端配置规范：

1. **transformBackendSearchToSchema** - 表单字段配置
2. **transformColumns** - 表格列配置
3. **transformToGroupedDescriptions** - 详情描述配置

## 1. 表单字段配置 (transformBackendSearchToSchema)

### 1.1 基础字段结构

```php
<?php
$field = [
    'field' => 'field_name',        // 字段名（必填）
    'title' => '字段标题',           // 字段标题（必填）
    'type' => 'input',              // 字段类型（必填）
    'required' => false,            // 是否必填
    'config' => [                   // 字段配置
        'placeholder' => '请输入',
        // 其他配置...
    ],
];
```

### 1.2 支持的字段类型

#### 1.2.1 文本输入框 (input)

```php
<?php
$inputField = [
    'field' => 'username',
    'title' => '用户名',
    'type' => 'input',
    'required' => true,
    'config' => [
        'placeholder' => '请输入用户名',
        'maxlength' => 50,
        'allowClear' => true,
        'showCount' => true,
    ],
];
```

#### 1.2.2 多行文本 (textarea)

```php
<?php
$textareaField = [
    'field' => 'description',
    'title' => '描述',
    'type' => 'textarea',
    'config' => [
        'placeholder' => '请输入描述信息',
        'rows' => 4,
        'maxlength' => 500,
        'showCount' => true,
        'allowClear' => true,
    ],
];
```

#### 1.2.3 数字输入 (number)

```php
<?php
$numberField = [
    'field' => 'price',
    'title' => '价格',
    'type' => 'number',
    'required' => true,
    'config' => [
        'placeholder' => '请输入价格',
        'precision' => 2,
        'min' => 0,
        'max' => 999999,
        'step' => 0.01,
        'prefix' => '¥',
        'suffix' => '元',
    ],
];
```

#### 1.2.4 下拉选择 (select)

```php
<?php
// 单选下拉
$selectField = [
    'field' => 'status',
    'title' => '状态',
    'type' => 'select',
    'required' => true,
    'config' => [
        'placeholder' => '请选择状态',
        'allowClear' => true,
        'showSearch' => true,
        'options' => [
            ['label' => '启用', 'value' => 1],
            ['label' => '禁用', 'value' => 0],
            ['label' => '待审核', 'value' => 2],
        ],
    ],
];

// 多选下拉
$multiSelectField = [
    'field' => 'tags',
    'title' => '标签',
    'type' => 'select',
    'config' => [
        'placeholder' => '请选择标签',
        'mode' => 'multiple',
        'maxTagCount' => 3,
        'options' => [
            ['label' => '热门', 'value' => 'hot'],
            ['label' => '推荐', 'value' => 'recommend'],
            ['label' => '新品', 'value' => 'new'],
        ],
    ],
];
```

#### 1.2.5 API下拉选择 (apiSelect)

```php
<?php
// 基础API选择
$apiSelectField = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'apiSelect',
    'required' => true,
    'config' => [
        'url' => '/api/departments',
        'placeholder' => '请选择部门',
        'labelField' => 'name',
        'valueField' => 'id',
        'immediate' => true,
        'showSearch' => true,
        'allowClear' => true,
        'params' => [
            'status' => 1,
        ],
    ],
];

// 带搜索的API选择
$searchableApiSelectField = [
    'field' => 'user_id',
    'title' => '用户',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/users/search',
        'placeholder' => '请输入用户名搜索',
        'labelField' => 'name',
        'valueField' => 'id',
        'showSearch' => true,
        'searchKey' => 'keyword',
        'debounceTime' => 300,
    ],
];
```

#### 1.2.6 日期选择 (date)

```php
<?php
$dateField = [
    'field' => 'birth_date',
    'title' => '出生日期',
    'type' => 'date',
    'config' => [
        'placeholder' => '请选择出生日期',
        'format' => 'YYYY-MM-DD',
        'valueFormat' => 'YYYY-MM-DD',
        'allowClear' => true,
        'disabledDate' => [
            'type' => 'future', // 禁用未来日期
        ],
    ],
];
```

#### 1.2.7 日期范围 (dateRange)

```php
<?php
$dateRangeField = [
    'field' => 'date_range',
    'title' => '日期范围',
    'type' => 'dateRange',
    'config' => [
        'placeholder' => ['开始日期', '结束日期'],
        'format' => 'YYYY-MM-DD',
        'valueFormat' => 'YYYY-MM-DD',
        'allowClear' => true,
        'separator' => '至',
    ],
    'fieldMappingTime' => [
        ['start_date', ['date_range'], [0]],
        ['end_date', ['date_range'], [1]],
    ],
];
```

#### 1.2.8 复选框 (checkbox)

```php
<?php
$checkboxField = [
    'field' => 'permissions',
    'title' => '权限',
    'type' => 'checkbox',
    'config' => [
        'options' => [
            ['label' => '查看', 'value' => 'view'],
            ['label' => '编辑', 'value' => 'edit'],
            ['label' => '删除', 'value' => 'delete'],
            ['label' => '导出', 'value' => 'export'],
        ],
    ],
];
```

#### 1.2.9 单选框 (radio)

```php
<?php
$radioField = [
    'field' => 'gender',
    'title' => '性别',
    'type' => 'radio',
    'required' => true,
    'config' => [
        'options' => [
            ['label' => '男', 'value' => 1],
            ['label' => '女', 'value' => 2],
            ['label' => '其他', 'value' => 3],
        ],
    ],
];
```

#### 1.2.10 开关 (switch)

```php
<?php
$switchField = [
    'field' => 'is_active',
    'title' => '是否启用',
    'type' => 'switch',
    'config' => [
        'checkedValue' => 1,
        'uncheckedValue' => 0,
        'checkedChildren' => '启用',
        'unCheckedChildren' => '禁用',
    ],
];
```

#### 1.2.11 文件上传 (Upload)

```php
<?php
// 单文件上传
$uploadField = [
    'field' => 'avatar',
    'title' => '头像',
    'type' => 'Upload',
    'config' => [
        'accept' => '.jpg,.jpeg,.png,.gif',
        'maxCount' => 1,
        'maxSize' => 2, // MB
        'listType' => 'picture-card',
        'showUploadList' => true,
        'customRequest' => true,
        'renderComponentContent' => '点击上传',
    ],
];

// 多文件上传
$multiUploadField = [
    'field' => 'attachments',
    'title' => '附件',
    'type' => 'Upload',
    'config' => [
        'accept' => '.pdf,.doc,.docx,.xls,.xlsx',
        'multiple' => true,
        'maxCount' => 5,
        'maxSize' => 10,
        'listType' => 'text',
        'renderComponentContent' => '选择文件',
    ],
];
```

#### 1.2.12 可编辑表格 (edittable)

```php
<?php
$edittableField = [
    'field' => 'order_items',
    'title' => '订单明细',
    'type' => 'edittable',
    'config' => [
        'columns' => [
            [
                'field' => 'product_name',
                'title' => '商品名称',
                'type' => 'input',
                'required' => true,
                'config' => [
                    'placeholder' => '请输入商品名称',
                ],
            ],
            [
                'field' => 'quantity',
                'title' => '数量',
                'type' => 'number',
                'required' => true,
                'config' => [
                    'min' => 1,
                    'precision' => 0,
                ],
            ],
            [
                'field' => 'unit_price',
                'title' => '单价',
                'type' => 'number',
                'required' => true,
                'config' => [
                    'min' => 0,
                    'precision' => 2,
                    'prefix' => '¥',
                ],
            ],
            [
                'field' => 'amount',
                'title' => '小计',
                'type' => 'number',
                'config' => [
                    'readonly' => true,
                    'precision' => 2,
                    'prefix' => '¥',
                ],
            ],
        ],
        'minRows' => 1,
        'maxRows' => 10,
        'showAddButton' => true,
        'showDeleteButton' => true,
    ],
];
```

#### 1.2.13 隐藏字段 (hidden)

```php
<?php
$hiddenField = [
    'field' => 'created_by',
    'title' => '创建人',
    'type' => 'hidden',
    'default' => 1, // 当前用户ID
];
```

### 1.3 计算字段配置

#### 方案一：config.calculation（推荐）

```php
<?php
$calculationField = [
    'field' => 'total_price',
    'title' => '总价',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'placeholder' => '自动计算',
        'calculation' => [
            'type' => 'product',                    // 计算类型
            'sourceFields' => ['unit_price', 'quantity'], // 参与计算的字段
            'triggerFields' => ['unit_price', 'quantity'], // 触发字段
            'precision' => 2,                       // 小数位数
            'defaultValue' => 0,                    // 默认值
            'realtime' => true,                     // 是否实时计算
        ],
    ],
];
```

#### 方案二：linkage.rules.calculation

```php
<?php
$linkageCalculationField = [
    'field' => 'total_price',
    'title' => '总价',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'placeholder' => '自动计算',
    ],
    'linkage' => [
        'triggerFields' => ['unit_price', 'quantity'],
        'rules' => [
            'calculation' => [
                'type' => 'product',
                'sourceFields' => ['unit_price', 'quantity'],
                'precision' => 2,
                'defaultValue' => 0,
            ],
        ],
    ],
];
```

### 1.4 支持的计算类型

#### 1.4.1 求和计算 (sum)

```php
<?php
// 基础求和 - 多个费用相加
$totalCostField = [
    'field' => 'total_cost',
    'title' => '总成本',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'placeholder' => '自动计算',
        'precision' => 2,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'sum',
            'sourceFields' => ['material_cost', 'labor_cost', 'overhead_cost'],
            'triggerFields' => ['material_cost', 'labor_cost', 'overhead_cost'],
            'precision' => 2,
            'defaultValue' => 0,
            'realtime' => true,
        ],
    ],
];

// 条件求和 - 只计算特定条件的字段
$conditionalSumField = [
    'field' => 'taxable_amount',
    'title' => '应税金额',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'calculation' => [
            'type' => 'sum',
            'sourceFields' => ['amount1', 'amount2', 'amount3'],
            'triggerFields' => ['amount1', 'amount2', 'amount3', 'tax_exempt'],
            'conditions' => [
                [
                    'condition' => [
                        'field' => 'tax_exempt',
                        'operator' => '=',
                        'value' => false,
                    ],
                    'calculation' => [
                        'type' => 'sum',
                        'sourceFields' => ['amount1', 'amount2', 'amount3'],
                        'precision' => 2,
                    ],
                ],
            ],
            'defaultValue' => 0,
        ],
    ],
];
```

#### 1.4.2 乘积计算 (product)

```php
<?php
// 基础乘积 - 单价 × 数量
$totalPriceField = [
    'field' => 'total_price',
    'title' => '总价',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'calculation' => [
            'type' => 'product',
            'sourceFields' => ['unit_price', 'quantity'],
            'triggerFields' => ['unit_price', 'quantity'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 多字段乘积 - 长×宽×高
$volumeField = [
    'field' => 'volume',
    'title' => '体积',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '立方米',
        'calculation' => [
            'type' => 'product',
            'sourceFields' => ['length', 'width', 'height'],
            'triggerFields' => ['length', 'width', 'height'],
            'precision' => 3,
            'defaultValue' => 0,
        ],
    ],
];

// 面积计算 - 长×宽
$areaField = [
    'field' => 'area',
    'title' => '面积',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '平方米',
        'calculation' => [
            'type' => 'product',
            'sourceFields' => ['length', 'width'],
            'triggerFields' => ['length', 'width'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];
```

#### 1.4.3 减法计算 (subtract)

```php
<?php
// 基础减法 - 收入 - 支出
$profitField = [
    'field' => 'profit',
    'title' => '利润',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'calculation' => [
            'type' => 'subtract',
            'sourceFields' => ['revenue', 'cost'],
            'triggerFields' => ['revenue', 'cost'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 余额计算 - 总额 - 已用金额
$balanceField = [
    'field' => 'balance',
    'title' => '余额',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'subtract',
            'sourceFields' => ['total_amount', 'used_amount'],
            'triggerFields' => ['total_amount', 'used_amount'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 库存计算 - 入库 - 出库
$stockField = [
    'field' => 'current_stock',
    'title' => '当前库存',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '件',
        'calculation' => [
            'type' => 'subtract',
            'sourceFields' => ['stock_in', 'stock_out'],
            'triggerFields' => ['stock_in', 'stock_out'],
            'precision' => 0,
            'defaultValue' => 0,
        ],
    ],
];
```

#### 1.4.4 除法计算 (divide)

```php
<?php
// 基础除法 - 平均值计算
$averageScoreField = [
    'field' => 'average_score',
    'title' => '平均分',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'calculation' => [
            'type' => 'divide',
            'sourceFields' => ['total_score', 'subject_count'],
            'triggerFields' => ['total_score', 'subject_count'],
            'precision' => 1,
            'defaultValue' => 0,
        ],
    ],
];

// 单价计算 - 总价 ÷ 数量
$unitPriceField = [
    'field' => 'unit_price',
    'title' => '单价',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'divide',
            'sourceFields' => ['total_amount', 'quantity'],
            'triggerFields' => ['total_amount', 'quantity'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 效率计算 - 产量 ÷ 时间
$efficiencyField = [
    'field' => 'efficiency',
    'title' => '生产效率',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '件/小时',
        'calculation' => [
            'type' => 'divide',
            'sourceFields' => ['output_quantity', 'work_hours'],
            'triggerFields' => ['output_quantity', 'work_hours'],
            'precision' => 1,
            'defaultValue' => 0,
        ],
    ],
];
```

#### 1.4.5 平均值计算 (average)

```php
<?php
// 简单平均值 - 多科成绩平均
$averageGradeField = [
    'field' => 'average_grade',
    'title' => '平均成绩',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '分',
        'calculation' => [
            'type' => 'average',
            'sourceFields' => ['math_score', 'english_score', 'science_score'],
            'triggerFields' => ['math_score', 'english_score', 'science_score'],
            'precision' => 1,
            'defaultValue' => 0,
        ],
    ],
];

// 多个评分的平均值
$averageRatingField = [
    'field' => 'average_rating',
    'title' => '平均评分',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '星',
        'calculation' => [
            'type' => 'average',
            'sourceFields' => ['service_rating', 'quality_rating', 'speed_rating'],
            'triggerFields' => ['service_rating', 'quality_rating', 'speed_rating'],
            'precision' => 1,
            'defaultValue' => 0,
        ],
    ],
];

// 月度平均销售额
$monthlyAverageField = [
    'field' => 'monthly_average_sales',
    'title' => '月均销售额',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'average',
            'sourceFields' => ['jan_sales', 'feb_sales', 'mar_sales', 'apr_sales'],
            'triggerFields' => ['jan_sales', 'feb_sales', 'mar_sales', 'apr_sales'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];
```

#### 1.4.6 自定义公式计算 (formula)

```php
<?php
// 折扣价计算 - 原价 × (1 - 折扣率/100)
$discountedPriceField = [
    'field' => 'discounted_price',
    'title' => '折扣价',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'formula',
            'formula' => 'original_price * (1 - discount_rate / 100)',
            'triggerFields' => ['original_price', 'discount_rate'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 复合利息计算 - P × (1 + r)^t
$compoundInterestField = [
    'field' => 'compound_interest',
    'title' => '复合利息',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'formula',
            'formula' => 'principal * Math.pow(1 + rate / 100, time) - principal',
            'triggerFields' => ['principal', 'rate', 'time'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// BMI计算 - 体重 / (身高/100)²
$bmiField = [
    'field' => 'bmi',
    'title' => 'BMI指数',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'calculation' => [
            'type' => 'formula',
            'formula' => 'weight / Math.pow(height / 100, 2)',
            'triggerFields' => ['weight', 'height'],
            'precision' => 1,
            'defaultValue' => 0,
        ],
    ],
];

// 利润率计算 - (收入 - 成本) / 收入 × 100
$profitMarginField = [
    'field' => 'profit_margin',
    'title' => '利润率',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '%',
        'calculation' => [
            'type' => 'formula',
            'formula' => '((revenue - cost) / revenue) * 100',
            'triggerFields' => ['revenue', 'cost'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];
```

#### 1.4.7 自定义函数计算 (custom)

```php
<?php
// 税后金额计算 - 使用自定义函数
$afterTaxAmountField = [
    'field' => 'after_tax_amount',
    'title' => '税后金额',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'custom',
            'customFunction' => 'calculateAfterTax',
            'triggerFields' => ['amount', 'tax_rate', 'tax_type'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 年龄计算 - 根据出生日期计算年龄
$ageField = [
    'field' => 'age',
    'title' => '年龄',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '岁',
        'calculation' => [
            'type' => 'custom',
            'customFunction' => 'calculateAge',
            'triggerFields' => ['birth_date'],
            'precision' => 0,
            'defaultValue' => 0,
        ],
    ],
];

// 工作日计算 - 排除周末和节假日
$workDaysField = [
    'field' => 'work_days',
    'title' => '工作日天数',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '天',
        'calculation' => [
            'type' => 'custom',
            'customFunction' => 'calculateWorkDays',
            'triggerFields' => ['start_date', 'end_date'],
            'precision' => 0,
            'defaultValue' => 0,
        ],
    ],
];

// 距离计算 - 根据经纬度计算距离
$distanceField = [
    'field' => 'distance',
    'title' => '距离',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '公里',
        'calculation' => [
            'type' => 'custom',
            'customFunction' => 'calculateDistance',
            'triggerFields' => ['lat1', 'lng1', 'lat2', 'lng2'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 信用评分计算 - 复杂的评分算法
$creditScoreField = [
    'field' => 'credit_score',
    'title' => '信用评分',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '分',
        'calculation' => [
            'type' => 'custom',
            'customFunction' => 'calculateCreditScore',
            'triggerFields' => ['income', 'debt', 'payment_history', 'credit_length'],
            'precision' => 0,
            'defaultValue' => 0,
        ],
    ],
];
```

#### 1.4.8 条件计算示例

```php
<?php
// 根据会员等级计算不同折扣
$memberDiscountField = [
    'field' => 'member_discount',
    'title' => '会员折扣',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '%',
        'calculation' => [
            'type' => 'custom',
            'triggerFields' => ['member_level', 'order_amount'],
            'conditions' => [
                [
                    'condition' => [
                        'field' => 'member_level',
                        'operator' => '=',
                        'value' => 'diamond',
                    ],
                    'calculation' => [
                        'type' => 'formula',
                        'formula' => 'order_amount >= 1000 ? 20 : 15', // 钻石会员
                        'precision' => 0,
                    ],
                ],
                [
                    'condition' => [
                        'field' => 'member_level',
                        'operator' => '=',
                        'value' => 'gold',
                    ],
                    'calculation' => [
                        'type' => 'formula',
                        'formula' => 'order_amount >= 500 ? 15 : 10', // 金卡会员
                        'precision' => 0,
                    ],
                ],
                [
                    'condition' => [
                        'field' => 'member_level',
                        'operator' => '=',
                        'value' => 'silver',
                    ],
                    'calculation' => [
                        'type' => 'formula',
                        'formula' => 'order_amount >= 200 ? 10 : 5', // 银卡会员
                        'precision' => 0,
                    ],
                ],
            ],
            'defaultValue' => 0, // 普通会员无折扣
        ],
    ],
];

// 根据订单金额和客户类型计算运费
$shippingFeeField = [
    'field' => 'shipping_fee',
    'title' => '运费',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'custom',
            'triggerFields' => ['order_amount', 'customer_type', 'shipping_method'],
            'conditions' => [
                [
                    'condition' => [
                        'field' => 'order_amount',
                        'operator' => '>=',
                        'value' => 500,
                    ],
                    'calculation' => [
                        'type' => 'formula',
                        'formula' => '0', // 满500免运费
                        'precision' => 2,
                    ],
                ],
                [
                    'condition' => [
                        'field' => 'customer_type',
                        'operator' => '=',
                        'value' => 'vip',
                    ],
                    'calculation' => [
                        'type' => 'formula',
                        'formula' => '0', // VIP客户免运费
                        'precision' => 2,
                    ],
                ],
                [
                    'condition' => [
                        'field' => 'shipping_method',
                        'operator' => '=',
                        'value' => 'express',
                    ],
                    'calculation' => [
                        'type' => 'formula',
                        'formula' => '25', // 快递费用
                        'precision' => 2,
                    ],
                ],
            ],
            'defaultValue' => 15, // 默认运费15元
        ],
    ],
];
```

#### 1.4.9 表格数据计算示例

```php
<?php
// 表格数据汇总 - 计算订单明细总金额
$orderTotalField = [
    'field' => 'order_total',
    'title' => '订单总金额',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'sum',
            'tableFields' => ['amount'], // 表格中的金额列
            'triggerFields' => ['order_items'], // 表格数据字段
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 表格数据计数 - 计算商品种类数量
$itemCountField = [
    'field' => 'item_count',
    'title' => '商品种类',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '种',
        'calculation' => [
            'type' => 'count',
            'tableFields' => ['product_id'], // 表格中的商品ID列
            'triggerFields' => ['order_items'],
            'precision' => 0,
            'defaultValue' => 0,
        ],
    ],
];

// 表格数据平均值 - 计算平均单价
$averageUnitPriceField = [
    'field' => 'average_unit_price',
    'title' => '平均单价',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'average',
            'tableFields' => ['unit_price'], // 表格中的单价列
            'triggerFields' => ['order_items'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 表格数据最大值 - 找出最高单价
$maxUnitPriceField = [
    'field' => 'max_unit_price',
    'title' => '最高单价',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'max',
            'tableFields' => ['unit_price'],
            'triggerFields' => ['order_items'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 表格数据最小值 - 找出最低单价
$minUnitPriceField = [
    'field' => 'min_unit_price',
    'title' => '最低单价',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'min',
            'tableFields' => ['unit_price'],
            'triggerFields' => ['order_items'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 表格数据条件计算 - 计算特定类别商品总金额
$categoryTotalField = [
    'field' => 'electronics_total',
    'title' => '电子产品总金额',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'sum',
            'tableFields' => ['amount'],
            'triggerFields' => ['order_items'],
            'filter' => [
                [
                    'field' => 'category',
                    'operator' => '=',
                    'value' => 'electronics',
                ],
            ],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 表格数据复杂计算 - 计算总重量
$totalWeightField = [
    'field' => 'total_weight',
    'title' => '总重量',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '公斤',
        'calculation' => [
            'type' => 'custom',
            'customFunction' => 'calculateTotalWeight',
            'tableFields' => ['quantity', 'unit_weight'],
            'triggerFields' => ['order_items'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 表格数据公式计算 - 计算总体积
$totalVolumeField = [
    'field' => 'total_volume',
    'title' => '总体积',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '立方米',
        'calculation' => [
            'type' => 'formula',
            'formula' => 'SUM(quantity * length * width * height / 1000000)', // 转换为立方米
            'tableFields' => ['quantity', 'length', 'width', 'height'],
            'triggerFields' => ['order_items'],
            'precision' => 3,
            'defaultValue' => 0,
        ],
    ],
];
```

#### 1.4.10 混合计算示例（表单字段 + 表格数据）

```php
<?php
// 含税总价 = 订单总金额 + 税费
$totalWithTaxField = [
    'field' => 'total_with_tax',
    'title' => '含税总价',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'formula',
            'formula' => 'order_total + tax_amount',
            'sourceFields' => ['tax_amount'], // 表单字段
            'tableFields' => ['amount'], // 表格字段（用于计算order_total）
            'triggerFields' => ['order_items', 'tax_amount'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 实付金额 = 含税总价 - 优惠金额
$actualPaymentField = [
    'field' => 'actual_payment',
    'title' => '实付金额',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'formula',
            'formula' => 'total_with_tax - discount_amount',
            'sourceFields' => ['discount_amount'], // 表单字段
            'tableFields' => ['amount'], // 表格字段（间接影响total_with_tax）
            'triggerFields' => ['order_items', 'tax_amount', 'discount_amount'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 利润计算 = 销售总额 - 成本总额
$totalProfitField = [
    'field' => 'total_profit',
    'title' => '总利润',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'prefix' => '¥',
        'calculation' => [
            'type' => 'formula',
            'formula' => 'SUM(selling_price * quantity) - SUM(cost_price * quantity)',
            'tableFields' => ['selling_price', 'cost_price', 'quantity'],
            'triggerFields' => ['order_items'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];

// 平均利润率 = 总利润 / 销售总额 * 100
$averageProfitMarginField = [
    'field' => 'average_profit_margin',
    'title' => '平均利润率',
    'type' => 'number',
    'config' => [
        'readonly' => true,
        'suffix' => '%',
        'calculation' => [
            'type' => 'formula',
            'formula' => '(total_profit / order_total) * 100',
            'sourceFields' => ['total_profit'], // 依赖其他计算字段
            'tableFields' => ['amount'],
            'triggerFields' => ['order_items'],
            'precision' => 2,
            'defaultValue' => 0,
        ],
    ],
];
```

### 1.5 联动配置

#### 1.5.1 显示/隐藏联动

```php
<?php
// 基础显示隐藏联动
$visibilityLinkageField = [
    'field' => 'city',
    'title' => '城市',
    'type' => 'select',
    'config' => [
        'placeholder' => '请选择城市',
        'options' => [
            ['label' => '广州', 'value' => 'guangzhou'],
            ['label' => '深圳', 'value' => 'shenzhen'],
            ['label' => '北京', 'value' => 'beijing'],
        ],
    ],
    'linkage' => [
        'triggerFields' => ['province'],
        'rules' => [
            'visibility' => [
                'showWhen' => [
                    [
                        'field' => 'province',
                        'operator' => '=',
                        'value' => 'guangdong',
                    ],
                ],
            ],
        ],
    ],
];

// 复杂条件显示隐藏
$complexVisibilityField = [
    'field' => 'discount_reason',
    'title' => '折扣原因',
    'type' => 'textarea',
    'config' => [
        'placeholder' => '请输入折扣原因',
        'rows' => 3,
    ],
    'linkage' => [
        'triggerFields' => ['discount_rate', 'customer_type'],
        'rules' => [
            'visibility' => [
                'showWhen' => [
                    [
                        'field' => 'discount_rate',
                        'operator' => '>',
                        'value' => 10,
                    ],
                    [
                        'field' => 'customer_type',
                        'operator' => 'in',
                        'value' => ['vip', 'gold'],
                        'logic' => 'or', // 与上一个条件的逻辑关系
                    ],
                ],
            ],
        ],
    ],
];
```

#### 1.5.2 选项过滤联动

```php
<?php
// 基础选项过滤
$optionsLinkageField = [
    'field' => 'city',
    'title' => '城市',
    'type' => 'select',
    'config' => [
        'placeholder' => '请选择城市',
    ],
    'linkage' => [
        'triggerFields' => ['province'],
        'rules' => [
            'options' => [
                'mapping' => [
                    'guangdong' => [
                        ['label' => '广州', 'value' => 'guangzhou'],
                        ['label' => '深圳', 'value' => 'shenzhen'],
                        ['label' => '珠海', 'value' => 'zhuhai'],
                    ],
                    'beijing' => [
                        ['label' => '朝阳区', 'value' => 'chaoyang'],
                        ['label' => '海淀区', 'value' => 'haidian'],
                        ['label' => '西城区', 'value' => 'xicheng'],
                    ],
                    'shanghai' => [
                        ['label' => '浦东新区', 'value' => 'pudong'],
                        ['label' => '黄浦区', 'value' => 'huangpu'],
                        ['label' => '徐汇区', 'value' => 'xuhui'],
                    ],
                ],
                'default' => [],
            ],
        ],
    ],
];

// API选项过滤联动
$apiOptionsLinkageField = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'apiSelect',
    'config' => [
        'url' => '/api/departments',
        'placeholder' => '请选择部门',
        'labelField' => 'name',
        'valueField' => 'id',
    ],
    'linkage' => [
        'triggerFields' => ['company_id'],
        'rules' => [
            'options' => [
                'apiParams' => [
                    'company_id' => '{company_id}', // 动态参数
                ],
                'clearOnTrigger' => true, // 触发时清空当前值
            ],
        ],
    ],
];
```

#### 1.5.3 字段属性联动

```php
<?php
// 只读状态联动
$readonlyLinkageField = [
    'field' => 'final_price',
    'title' => '最终价格',
    'type' => 'number',
    'config' => [
        'precision' => 2,
        'prefix' => '¥',
    ],
    'linkage' => [
        'triggerFields' => ['is_discount'],
        'rules' => [
            'componentProps' => [
                'readonly' => [
                    'when' => [
                        [
                            'field' => 'is_discount',
                            'operator' => '=',
                            'value' => 1,
                        ],
                    ],
                    'then' => true,
                    'else' => false,
                ],
            ],
        ],
    ],
];

// 必填状态联动
$requiredLinkageField = [
    'field' => 'id_number',
    'title' => '身份证号',
    'type' => 'input',
    'config' => [
        'placeholder' => '请输入身份证号',
        'maxlength' => 18,
    ],
    'linkage' => [
        'triggerFields' => ['user_type'],
        'rules' => [
            'required' => [
                'when' => [
                    [
                        'field' => 'user_type',
                        'operator' => '=',
                        'value' => 'individual',
                    ],
                ],
                'then' => true,
                'else' => false,
            ],
        ],
    ],
];
```

### 1.6 联动赋值配置

#### 1.6.1 基础联动赋值

```php
<?php
// 选择部门后自动填充部门名称和负责人
$basicLinkageAssignmentField = [
    'field' => 'department_id',
    'title' => '部门',
    'type' => 'apiSelect',
    'required' => true,
    'config' => [
        'url' => '/api/departments',
        'placeholder' => '请选择部门',
        'labelField' => 'name',
        'valueField' => 'id',
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'department_name',
                    'valueMapping' => 'label', // 使用选项的label值
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'manager_id',
                    'valueMapping' => 'manager_id', // 使用选项数据中的manager_id字段
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'manager_name',
                    'valueMapping' => 'manager_name',
                    'clearOnEmpty' => true,
                ],
            ],
        ],
    ],
];
```

#### 1.6.2 复杂联动赋值

```php
<?php
// 选择商品后自动填充价格、库存等信息
$complexLinkageAssignmentField = [
    'field' => 'product_id',
    'title' => '商品',
    'type' => 'apiSelect',
    'required' => true,
    'config' => [
        'url' => '/api/products',
        'placeholder' => '请选择商品',
        'labelField' => 'name',
        'valueField' => 'id',
        'showSearch' => true,
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'product_name',
                    'valueMapping' => 'label',
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'unit_price',
                    'valueMapping' => 'price',
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'stock_quantity',
                    'valueMapping' => 'stock',
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'category_id',
                    'valueMapping' => 'category_id',
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'category_name',
                    'valueMapping' => 'category_name',
                    'clearOnEmpty' => true,
                ],
            ],
        ],
    ],
];
```

#### 1.6.3 条件联动赋值

```php
<?php
// 根据客户类型选择不同的价格策略
$conditionalLinkageAssignmentField = [
    'field' => 'customer_id',
    'title' => '客户',
    'type' => 'apiSelect',
    'required' => true,
    'config' => [
        'url' => '/api/customers',
        'placeholder' => '请选择客户',
        'labelField' => 'name',
        'valueField' => 'id',
        'linkageAssignment' => [
            'targetFields' => [
                [
                    'field' => 'customer_name',
                    'valueMapping' => 'label',
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'customer_type',
                    'valueMapping' => 'type',
                    'clearOnEmpty' => true,
                ],
                [
                    'field' => 'discount_rate',
                    'valueMapping' => 'discount_rate',
                    'clearOnEmpty' => true,
                    'condition' => [
                        'field' => 'type',
                        'operator' => 'in',
                        'value' => ['vip', 'gold'],
                    ],
                ],
                [
                    'field' => 'credit_limit',
                    'valueMapping' => 'credit_limit',
                    'clearOnEmpty' => true,
                    'condition' => [
                        'field' => 'type',
                        'operator' => '!=',
                        'value' => 'cash',
                    ],
                ],
            ],
        ],
    ],
];
```

#### 1.6.4 多级联动赋值

```php
<?php
// 选择省份后联动城市，选择城市后联动区域
$provinceCityDistrictFields = [
    // 省份字段
    [
        'field' => 'province_id',
        'title' => '省份',
        'type' => 'apiSelect',
        'required' => true,
        'config' => [
            'url' => '/api/provinces',
            'placeholder' => '请选择省份',
            'labelField' => 'name',
            'valueField' => 'id',
            'linkageAssignment' => [
                'targetFields' => [
                    [
                        'field' => 'province_name',
                        'valueMapping' => 'label',
                        'clearOnEmpty' => true,
                    ],
                    [
                        'field' => 'city_id',
                        'valueMapping' => null, // 清空城市选择
                        'clearOnEmpty' => true,
                    ],
                    [
                        'field' => 'district_id',
                        'valueMapping' => null, // 清空区域选择
                        'clearOnEmpty' => true,
                    ],
                ],
            ],
        ],
    ],

    // 城市字段
    [
        'field' => 'city_id',
        'title' => '城市',
        'type' => 'apiSelect',
        'config' => [
            'url' => '/api/cities',
            'placeholder' => '请选择城市',
            'labelField' => 'name',
            'valueField' => 'id',
            'params' => [
                'province_id' => '{province_id}', // 动态参数
            ],
            'linkageAssignment' => [
                'targetFields' => [
                    [
                        'field' => 'city_name',
                        'valueMapping' => 'label',
                        'clearOnEmpty' => true,
                    ],
                    [
                        'field' => 'district_id',
                        'valueMapping' => null, // 清空区域选择
                        'clearOnEmpty' => true,
                    ],
                ],
            ],
        ],
        'linkage' => [
            'triggerFields' => ['province_id'],
            'rules' => [
                'visibility' => [
                    'showWhen' => [
                        [
                            'field' => 'province_id',
                            'operator' => '!=',
                            'value' => null,
                        ],
                    ],
                ],
            ],
        ],
    ],

    // 区域字段
    [
        'field' => 'district_id',
        'title' => '区域',
        'type' => 'apiSelect',
        'config' => [
            'url' => '/api/districts',
            'placeholder' => '请选择区域',
            'labelField' => 'name',
            'valueField' => 'id',
            'params' => [
                'city_id' => '{city_id}', // 动态参数
            ],
            'linkageAssignment' => [
                'targetFields' => [
                    [
                        'field' => 'district_name',
                        'valueMapping' => 'label',
                        'clearOnEmpty' => true,
                    ],
                ],
            ],
        ],
        'linkage' => [
            'triggerFields' => ['city_id'],
            'rules' => [
                'visibility' => [
                    'showWhen' => [
                        [
                            'field' => 'city_id',
                            'operator' => '!=',
                            'value' => null,
                        ],
                    ],
                ],
            ],
        ],
    ],
];
```

## 2. 表格列配置 (transformColumns)

### 2.1 基础列结构

```php
<?php
$column = [
    'field' => 'column_name',       // 列字段名（必填）
    'title' => '列标题',            // 列标题（必填）
    'type' => 'text',               // 列类型（必填）
    'width' => 120,                 // 列宽度
    'visible' => true,              // 是否可见
    'sortable' => false,            // 是否可排序
    'fixed' => null,                // 固定位置：'left' | 'right'
];
```

### 2.2 支持的列类型

#### 2.2.1 文本显示 (text)

```php
<?php
$textColumn = [
    'field' => 'name',
    'title' => '姓名',
    'type' => 'text',
    'width' => 120,
    'sortable' => true,
    'config' => [
        'ellipsis' => true, // 超长省略
        'copyable' => true, // 可复制
    ],
];
```

#### 2.2.2 数字显示 (number)

```php
<?php
$numberColumn = [
    'field' => 'price',
    'title' => '价格',
    'type' => 'number',
    'width' => 100,
    'sortable' => true,
    'config' => [
        'precision' => 2,
        'prefix' => '¥',
        'suffix' => '元',
        'separator' => ',', // 千分位分隔符
    ],
];
```

#### 2.2.3 日期显示 (date)

```php
<?php
$dateColumn = [
    'field' => 'birth_date',
    'title' => '出生日期',
    'type' => 'date',
    'width' => 120,
    'sortable' => true,
    'config' => [
        'format' => 'YYYY-MM-DD',
        'emptyText' => '-',
    ],
];
```

#### 2.2.4 日期时间显示 (datetime)

```php
<?php
$datetimeColumn = [
    'field' => 'created_at',
    'title' => '创建时间',
    'type' => 'datetime',
    'width' => 160,
    'sortable' => true,
    'config' => [
        'format' => 'YYYY-MM-DD HH:mm:ss',
        'emptyText' => '-',
    ],
];
```

#### 2.2.5 选择显示 (select)

```php
<?php
$selectColumn = [
    'field' => 'status',
    'title' => '状态',
    'type' => 'select',
    'width' => 100,
    'config' => [
        'options' => [
            ['label' => '启用', 'value' => 1, 'color' => 'green'],
            ['label' => '禁用', 'value' => 0, 'color' => 'red'],
            ['label' => '待审核', 'value' => 2, 'color' => 'orange'],
        ],
        'showColor' => true, // 显示颜色标识
    ],
];
```

#### 2.2.6 开关显示 (switch)

```php
<?php
$switchColumn = [
    'field' => 'is_active',
    'title' => '状态',
    'type' => 'switch',
    'width' => 80,
    'config' => [
        'checkedValue' => 1,
        'uncheckedValue' => 0,
        'checkedText' => '启用',
        'uncheckedText' => '禁用',
        'disabled' => true, // 只显示不可操作
    ],
];
```

#### 2.2.7 图片显示 (image)

```php
<?php
$imageColumn = [
    'field' => 'avatar',
    'title' => '头像',
    'type' => 'image',
    'width' => 80,
    'config' => [
        'width' => 60,
        'height' => 60,
        'fit' => 'cover',
        'preview' => true, // 支持预览
        'fallback' => '/default-avatar.png', // 默认图片
    ],
];
```

#### 2.2.8 链接显示 (link)

```php
<?php
$linkColumn = [
    'field' => 'website',
    'title' => '网站',
    'type' => 'link',
    'width' => 150,
    'config' => [
        'target' => '_blank',
        'ellipsis' => true,
        'copyable' => true,
    ],
];
```

### 2.3 操作按钮配置

#### 2.3.1 基础操作按钮

```php
<?php
$basicActionsColumn = [
    'field' => 'actions',
    'title' => '操作',
    'type' => 'actions',
    'width' => 150,
    'fixed' => 'right',
    'config' => [
        'buttons' => [
            [
                'title' => '编辑',
                'type' => 'edit',
                'key' => 'edit',
                'icon' => 'EditOutlined',
            ],
            [
                'title' => '删除',
                'type' => 'delete',
                'key' => 'delete',
                'icon' => 'DeleteOutlined',
                'confirm' => '确定要删除这条记录吗？',
            ],
            [
                'title' => '查看',
                'type' => 'view',
                'key' => 'view',
                'icon' => 'EyeOutlined',
            ],
        ],
    ],
];
```

#### 2.3.2 条件显示按钮

```php
<?php
$conditionalActionsColumn = [
    'field' => 'actions',
    'title' => '操作',
    'type' => 'actions',
    'width' => 180,
    'fixed' => 'right',
    'config' => [
        'buttons' => [
            [
                'title' => '编辑',
                'type' => 'edit',
                'key' => 'edit',
                'show' => [
                    'field' => 'status',
                    'operator' => '!=',
                    'value' => 'locked',
                ],
            ],
            [
                'title' => '启用',
                'type' => 'enable',
                'key' => 'enable',
                'show' => [
                    'field' => 'status',
                    'operator' => '=',
                    'value' => 'disabled',
                ],
            ],
            [
                'title' => '禁用',
                'type' => 'disable',
                'key' => 'disable',
                'show' => [
                    'field' => 'status',
                    'operator' => '=',
                    'value' => 'enabled',
                ],
            ],
            [
                'title' => '删除',
                'type' => 'delete',
                'key' => 'delete',
                'show' => [
                    'field' => 'can_delete',
                    'operator' => '=',
                    'value' => true,
                ],
                'confirm' => '确定要删除这条记录吗？',
            ],
        ],
    ],
];
```

#### 2.3.3 外部链接按钮

```php
<?php
$linkActionsColumn = [
    'field' => 'actions',
    'title' => '操作',
    'type' => 'actions',
    'width' => 200,
    'fixed' => 'right',
    'config' => [
        'buttons' => [
            [
                'title' => '查看详情',
                'type' => 'link',
                'key' => 'detail',
                'url' => '/user/detail/{id}',
                'target' => '_blank',
            ],
            [
                'title' => '编辑资料',
                'type' => 'link',
                'key' => 'edit_profile',
                'url' => '/user/edit/{id}',
                'params' => [
                    'tab' => 'profile',
                ],
            ],
            [
                'title' => '下载报告',
                'type' => 'download',
                'key' => 'download',
                'url' => '/api/reports/{id}/download',
                'method' => 'GET',
            ],
        ],
    ],
];
```

#### 2.3.4 下拉菜单按钮

```php
<?php
$dropdownActionsColumn = [
    'field' => 'actions',
    'title' => '操作',
    'type' => 'actions',
    'width' => 120,
    'fixed' => 'right',
    'config' => [
        'buttons' => [
            [
                'title' => '编辑',
                'type' => 'edit',
                'key' => 'edit',
            ],
            [
                'title' => '更多',
                'type' => 'dropdown',
                'key' => 'more',
                'children' => [
                    [
                        'title' => '复制',
                        'type' => 'copy',
                        'key' => 'copy',
                    ],
                    [
                        'title' => '导出',
                        'type' => 'export',
                        'key' => 'export',
                    ],
                    [
                        'title' => '归档',
                        'type' => 'archive',
                        'key' => 'archive',
                        'confirm' => '确定要归档这条记录吗？',
                    ],
                    [
                        'title' => '删除',
                        'type' => 'delete',
                        'key' => 'delete',
                        'confirm' => '确定要删除这条记录吗？',
                        'danger' => true,
                    ],
                ],
            ],
        ],
    ],
];
```

#### 2.3.5 自定义按钮样式

```php
<?php
$styledActionsColumn = [
    'field' => 'actions',
    'title' => '操作',
    'type' => 'actions',
    'width' => 200,
    'fixed' => 'right',
    'config' => [
        'buttons' => [
            [
                'title' => '通过',
                'type' => 'approve',
                'key' => 'approve',
                'color' => 'success',
                'size' => 'small',
                'show' => [
                    'field' => 'status',
                    'operator' => '=',
                    'value' => 'pending',
                ],
            ],
            [
                'title' => '拒绝',
                'type' => 'reject',
                'key' => 'reject',
                'color' => 'danger',
                'size' => 'small',
                'show' => [
                    'field' => 'status',
                    'operator' => '=',
                    'value' => 'pending',
                ],
                'confirm' => '确定要拒绝这个申请吗？',
            ],
            [
                'title' => '重新提交',
                'type' => 'resubmit',
                'key' => 'resubmit',
                'color' => 'warning',
                'size' => 'small',
                'show' => [
                    'field' => 'status',
                    'operator' => '=',
                    'value' => 'rejected',
                ],
            ],
        ],
    ],
];
```

## 3. 详情描述配置 (transformToGroupedDescriptions)

### 3.1 基础描述结构

```php
<?php
$description = [
    'field' => 'field_name',        // 字段名（必填）
    'title' => '字段标题',           // 字段标题（必填）
    'type' => 'text',               // 显示类型（必填）
    'span' => 1,                    // 占用列数（1-4）
];
```

### 3.2 支持的显示类型

#### 3.2.1 文本显示 (text)

```php
<?php
$textDescription = [
    'field' => 'name',
    'title' => '姓名',
    'type' => 'text',
    'span' => 1,
    'config' => [
        'copyable' => true, // 可复制
        'ellipsis' => true, // 超长省略
    ],
];
```

#### 3.2.2 数字显示 (number)

```php
<?php
$numberDescription = [
    'field' => 'salary',
    'title' => '薪资',
    'type' => 'number',
    'span' => 1,
    'config' => [
        'precision' => 2,
        'prefix' => '¥',
        'suffix' => '元',
        'separator' => ',', // 千分位分隔符
    ],
];
```

#### 3.2.3 日期显示 (date)

```php
<?php
$dateDescription = [
    'field' => 'birth_date',
    'title' => '出生日期',
    'type' => 'date',
    'span' => 1,
    'config' => [
        'format' => 'YYYY-MM-DD',
        'emptyText' => '未设置',
    ],
];
```

#### 3.2.4 日期时间显示 (datetime)

```php
<?php
$datetimeDescription = [
    'field' => 'created_at',
    'title' => '创建时间',
    'type' => 'datetime',
    'span' => 2,
    'config' => [
        'format' => 'YYYY-MM-DD HH:mm:ss',
        'emptyText' => '-',
    ],
];
```

#### 3.2.5 选择显示 (select)

```php
<?php
$selectDescription = [
    'field' => 'status',
    'title' => '状态',
    'type' => 'select',
    'span' => 1,
    'config' => [
        'options' => [
            ['label' => '启用', 'value' => 1, 'color' => 'green'],
            ['label' => '禁用', 'value' => 0, 'color' => 'red'],
            ['label' => '待审核', 'value' => 2, 'color' => 'orange'],
        ],
        'showColor' => true, // 显示颜色标识
    ],
];
```

#### 3.2.6 开关显示 (switch)

```php
<?php
$switchDescription = [
    'field' => 'is_active',
    'title' => '是否启用',
    'type' => 'switch',
    'span' => 1,
    'config' => [
        'checkedValue' => 1,
        'uncheckedValue' => 0,
        'checkedText' => '启用',
        'uncheckedText' => '禁用',
    ],
];
```

#### 3.2.7 图片显示 (image)

```php
<?php
$imageDescription = [
    'field' => 'avatar',
    'title' => '头像',
    'type' => 'image',
    'span' => 1,
    'config' => [
        'width' => 100,
        'height' => 100,
        'fit' => 'cover',
        'preview' => true, // 支持预览
        'fallback' => '/default-avatar.png',
    ],
];
```

#### 3.2.8 链接显示 (link)

```php
<?php
$linkDescription = [
    'field' => 'website',
    'title' => '个人网站',
    'type' => 'link',
    'span' => 2,
    'config' => [
        'target' => '_blank',
        'copyable' => true,
    ],
];
```

#### 3.2.9 标签显示 (tag)

```php
<?php
$tagDescription = [
    'field' => 'tags',
    'title' => '标签',
    'type' => 'tag',
    'span' => 2,
    'config' => [
        'color' => 'blue',
        'multiple' => true, // 多个标签
        'separator' => ',', // 分隔符
    ],
];
```

### 3.3 分组配置

#### 3.3.1 基础分组配置

```php
<?php
$basicGroupedDescriptions = [
    [
        'label' => '基本信息',
        'items' => [
            [
                'field' => 'name',
                'title' => '姓名',
                'type' => 'text',
                'span' => 1,
            ],
            [
                'field' => 'age',
                'title' => '年龄',
                'type' => 'number',
                'span' => 1,
                'config' => [
                    'suffix' => '岁',
                ],
            ],
            [
                'field' => 'gender',
                'title' => '性别',
                'type' => 'select',
                'span' => 1,
                'config' => [
                    'options' => [
                        ['label' => '男', 'value' => 1],
                        ['label' => '女', 'value' => 2],
                    ],
                ],
            ],
            [
                'field' => 'birth_date',
                'title' => '出生日期',
                'type' => 'date',
                'span' => 1,
                'config' => [
                    'format' => 'YYYY-MM-DD',
                ],
            ],
        ],
    ],
    [
        'label' => '联系信息',
        'items' => [
            [
                'field' => 'email',
                'title' => '邮箱',
                'type' => 'text',
                'span' => 2,
                'config' => [
                    'copyable' => true,
                ],
            ],
            [
                'field' => 'phone',
                'title' => '电话',
                'type' => 'text',
                'span' => 2,
                'config' => [
                    'copyable' => true,
                ],
            ],
        ],
    ],
];
```

#### 3.3.2 复杂分组配置

```php
<?php
$complexGroupedDescriptions = [
    [
        'label' => '用户信息',
        'items' => [
            [
                'field' => 'avatar',
                'title' => '头像',
                'type' => 'image',
                'span' => 1,
                'config' => [
                    'width' => 80,
                    'height' => 80,
                    'preview' => true,
                ],
            ],
            [
                'field' => 'username',
                'title' => '用户名',
                'type' => 'text',
                'span' => 1,
                'config' => [
                    'copyable' => true,
                ],
            ],
            [
                'field' => 'real_name',
                'title' => '真实姓名',
                'type' => 'text',
                'span' => 1,
            ],
            [
                'field' => 'status',
                'title' => '状态',
                'type' => 'switch',
                'span' => 1,
                'config' => [
                    'checkedValue' => 1,
                    'uncheckedValue' => 0,
                    'checkedText' => '启用',
                    'uncheckedText' => '禁用',
                ],
            ],
        ],
    ],
    [
        'label' => '职业信息',
        'items' => [
            [
                'field' => 'department_name',
                'title' => '部门',
                'type' => 'text',
                'span' => 1,
            ],
            [
                'field' => 'position',
                'title' => '职位',
                'type' => 'text',
                'span' => 1,
            ],
            [
                'field' => 'salary',
                'title' => '薪资',
                'type' => 'number',
                'span' => 1,
                'config' => [
                    'precision' => 2,
                    'prefix' => '¥',
                    'separator' => ',',
                ],
            ],
            [
                'field' => 'hire_date',
                'title' => '入职日期',
                'type' => 'date',
                'span' => 1,
                'config' => [
                    'format' => 'YYYY-MM-DD',
                ],
            ],
        ],
    ],
    [
        'label' => '权限信息',
        'items' => [
            [
                'field' => 'roles',
                'title' => '角色',
                'type' => 'tag',
                'span' => 2,
                'config' => [
                    'multiple' => true,
                    'separator' => ',',
                    'color' => 'blue',
                ],
            ],
            [
                'field' => 'permissions',
                'title' => '权限',
                'type' => 'tag',
                'span' => 2,
                'config' => [
                    'multiple' => true,
                    'separator' => ',',
                    'color' => 'green',
                ],
            ],
        ],
    ],
    [
        'label' => '时间信息',
        'items' => [
            [
                'field' => 'created_at',
                'title' => '创建时间',
                'type' => 'datetime',
                'span' => 2,
                'config' => [
                    'format' => 'YYYY-MM-DD HH:mm:ss',
                ],
            ],
            [
                'field' => 'updated_at',
                'title' => '更新时间',
                'type' => 'datetime',
                'span' => 2,
                'config' => [
                    'format' => 'YYYY-MM-DD HH:mm:ss',
                ],
            ],
        ],
    ],
];
```

#### 3.3.3 动态分组配置

```php
<?php
$dynamicGroupedDescriptions = [
    [
        'label' => '基本信息',
        'collapsible' => true, // 可折叠
        'defaultCollapsed' => false, // 默认展开
        'items' => [
            [
                'field' => 'name',
                'title' => '姓名',
                'type' => 'text',
                'span' => 1,
            ],
            [
                'field' => 'email',
                'title' => '邮箱',
                'type' => 'text',
                'span' => 1,
            ],
        ],
    ],
    [
        'label' => '详细信息',
        'collapsible' => true,
        'defaultCollapsed' => true, // 默认折叠
        'items' => [
            [
                'field' => 'address',
                'title' => '地址',
                'type' => 'text',
                'span' => 4, // 占满整行
            ],
            [
                'field' => 'description',
                'title' => '描述',
                'type' => 'text',
                'span' => 4,
                'config' => [
                    'ellipsis' => true,
                    'rows' => 3, // 多行显示
                ],
            ],
        ],
    ],
];
```

## 4. 完整控制器示例

### 4.1 用户管理控制器

```php
<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;

class UserController extends Controller
{
    /**
     * 获取表单配置
     */
    public function getFormConfig()
    {
        return response()->json([
            'fields' => [
                // 基础信息
                [
                    'field' => 'name',
                    'title' => '姓名',
                    'type' => 'input',
                    'required' => true,
                    'config' => [
                        'placeholder' => '请输入姓名',
                        'maxlength' => 50,
                    ],
                ],
                [
                    'field' => 'email',
                    'title' => '邮箱',
                    'type' => 'input',
                    'required' => true,
                    'config' => [
                        'placeholder' => '请输入邮箱',
                        'type' => 'email',
                    ],
                ],
                [
                    'field' => 'department_id',
                    'title' => '部门',
                    'type' => 'apiSelect',
                    'required' => true,
                    'config' => [
                        'url' => '/api/departments',
                        'placeholder' => '请选择部门',
                        'linkageAssignment' => [
                            'targetFields' => [
                                [
                                    'field' => 'department_name',
                                    'valueMapping' => 'label',
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'field' => 'department_name',
                    'title' => '部门名称',
                    'type' => 'input',
                    'config' => [
                        'readonly' => true,
                        'placeholder' => '自动填充',
                    ],
                ],

                // 薪资计算
                [
                    'field' => 'base_salary',
                    'title' => '基本工资',
                    'type' => 'number',
                    'config' => [
                        'precision' => 2,
                        'min' => 0,
                        'placeholder' => '请输入基本工资',
                    ],
                ],
                [
                    'field' => 'bonus_rate',
                    'title' => '奖金比例(%)',
                    'type' => 'number',
                    'config' => [
                        'precision' => 1,
                        'min' => 0,
                        'max' => 100,
                        'placeholder' => '请输入奖金比例',
                    ],
                ],
                [
                    'field' => 'total_salary',
                    'title' => '总薪资',
                    'type' => 'number',
                    'config' => [
                        'readonly' => true,
                        'placeholder' => '自动计算',
                        'calculation' => [
                            'type' => 'formula',
                            'formula' => 'base_salary * (1 + bonus_rate / 100)',
                            'triggerFields' => ['base_salary', 'bonus_rate'],
                            'precision' => 2,
                            'defaultValue' => 0,
                        ],
                    ],
                ],
            ],
        ]);
    }

    /**
     * 获取表格列配置
     */
    public function getTableColumns()
    {
        return response()->json([
            'columns' => [
                [
                    'field' => 'id',
                    'title' => 'ID',
                    'type' => 'number',
                    'width' => 80,
                    'fixed' => 'left',
                ],
                [
                    'field' => 'name',
                    'title' => '姓名',
                    'type' => 'text',
                    'width' => 120,
                ],
                [
                    'field' => 'email',
                    'title' => '邮箱',
                    'type' => 'text',
                    'width' => 200,
                ],
                [
                    'field' => 'department_name',
                    'title' => '部门',
                    'type' => 'text',
                    'width' => 120,
                ],
                [
                    'field' => 'total_salary',
                    'title' => '总薪资',
                    'type' => 'number',
                    'width' => 120,
                    'config' => [
                        'precision' => 2,
                        'prefix' => '¥',
                    ],
                ],
                [
                    'field' => 'status',
                    'title' => '状态',
                    'type' => 'switch',
                    'width' => 80,
                    'config' => [
                        'checkedValue' => 1,
                        'uncheckedValue' => 0,
                    ],
                ],
                [
                    'field' => 'created_at',
                    'title' => '创建时间',
                    'type' => 'datetime',
                    'width' => 160,
                    'config' => [
                        'format' => 'YYYY-MM-DD HH:mm:ss',
                    ],
                ],
                [
                    'field' => 'actions',
                    'title' => '操作',
                    'type' => 'actions',
                    'width' => 150,
                    'fixed' => 'right',
                    'config' => [
                        'buttons' => [
                            [
                                'title' => '编辑',
                                'type' => 'edit',
                                'key' => 'edit',
                            ],
                            [
                                'title' => '删除',
                                'type' => 'delete',
                                'key' => 'delete',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /**
     * 获取详情描述配置
     */
    public function getDetailConfig()
    {
        return response()->json([
            'descriptions' => [
                [
                    'label' => '基本信息',
                    'items' => [
                        [
                            'field' => 'name',
                            'title' => '姓名',
                            'type' => 'text',
                            'span' => 1,
                        ],
                        [
                            'field' => 'email',
                            'title' => '邮箱',
                            'type' => 'text',
                            'span' => 1,
                        ],
                        [
                            'field' => 'department_name',
                            'title' => '部门',
                            'type' => 'text',
                            'span' => 1,
                        ],
                        [
                            'field' => 'status',
                            'title' => '状态',
                            'type' => 'switch',
                            'span' => 1,
                            'config' => [
                                'checkedValue' => 1,
                                'uncheckedValue' => 0,
                            ],
                        ],
                    ],
                ],
                [
                    'label' => '薪资信息',
                    'items' => [
                        [
                            'field' => 'base_salary',
                            'title' => '基本工资',
                            'type' => 'number',
                            'span' => 1,
                            'config' => [
                                'precision' => 2,
                                'prefix' => '¥',
                            ],
                        ],
                        [
                            'field' => 'bonus_rate',
                            'title' => '奖金比例',
                            'type' => 'number',
                            'span' => 1,
                            'config' => [
                                'precision' => 1,
                                'suffix' => '%',
                            ],
                        ],
                        [
                            'field' => 'total_salary',
                            'title' => '总薪资',
                            'type' => 'number',
                            'span' => 2,
                            'config' => [
                                'precision' => 2,
                                'prefix' => '¥',
                            ],
                        ],
                    ],
                ],
                [
                    'label' => '时间信息',
                    'items' => [
                        [
                            'field' => 'created_at',
                            'title' => '创建时间',
                            'type' => 'datetime',
                            'span' => 2,
                            'config' => [
                                'format' => 'YYYY-MM-DD HH:mm:ss',
                            ],
                        ],
                        [
                            'field' => 'updated_at',
                            'title' => '更新时间',
                            'type' => 'datetime',
                            'span' => 2,
                            'config' => [
                                'format' => 'YYYY-MM-DD HH:mm:ss',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }
}
```

## 5. 最佳实践

### 5.1 配置规范

- **字段命名**：使用下划线命名法，如 `user_name`
- **类型选择**：根据数据类型选择合适的字段类型
- **必填验证**：合理设置必填字段
- **默认值**：为字段设置合理的默认值

### 5.2 性能优化

- **API选择器**：合理使用分页和搜索功能
- **计算字段**：避免过于复杂的计算逻辑
- **联动配置**：减少不必要的联动关系

### 5.3 用户体验

- **占位符**：提供有意义的占位符文本
- **错误提示**：设置清晰的验证错误信息
- **加载状态**：为异步操作提供加载提示

### 5.4 维护性

- **代码组织**：将相关配置组织在一起
- **注释说明**：为复杂配置添加注释
- **版本控制**：记录配置变更历史

## 6. 常见问题

### Q: 如何处理复杂的联动关系？

A: 使用 `linkage.rules` 配置多种联动规则，包括显示/隐藏、选项过滤、计算等。

### Q: 计算字段支持哪些数据源？

A: 支持表单字段值、表格数据汇总、静态值等多种数据源。

### Q: 如何优化大量数据的表格性能？

A: 使用虚拟滚动、分页加载、列宽固定等优化策略。

### Q: 详情页面如何实现动态布局？

A: 通过 `span` 属性控制字段占用列数，实现灵活的布局。

## 总结

本文档提供了三个核心转换方法的完整 PHP 后端配置指南：

✅ **表单配置**：支持各种字段类型、计算字段、联动功能✅ **表格配置**：支持多种列类型、操作按钮、固定列✅ **详情配置**：支持分组显示、多种显示类型、灵活布局✅ **统一规范**：保持一致的配置结构和命名规范

通过这些配置，开发者可以快速构建功能完整、用户体验良好的管理界面。
