import type {
  BackendSearchItem,
  DirectGroupedSearchData,
  GroupedSearchData,
} from './types';

import type { VbenFormSchema } from '#/adapter/form';

import { baseRequestClient } from '#/api/request';
import { normalizeOptions } from '#/utils/options';
import {
  stringToFileList,
  stringToFileObject,
} from '#/utils/upload/stringToFileObject';

import {
  createApiSelectPaginatedFunction,
  createListActionFunction,
  createSearchableListActionFunction,
  defaultFilterTreeNode,
} from './api-request';
import { LinkageRuleConverter } from './linkage-converter';
import { TYPE_TO_COMPONENT_MAP } from './types';

/**
 * 创建数据处理器函数
 * @param processor 处理器配置（函数、字符串或配置对象）
 * @param _type 处理器类型（beforeFetch 或 afterFetch）
 * @returns 处理器函数
 */
function createDataProcessor(
  processor: any,
  _type: 'afterFetch' | 'beforeFetch',
) {
  // 如果已经是函数，直接返回
  if (typeof processor === 'function') {
    return processor;
  }

  // 如果是字符串，尝试从全局处理器中获取
  if (typeof processor === 'string') {
    return async (data: any) => {
      // 尝试从多个可能的全局对象中获取处理函数
      const globalProcessors =
        (window as any).apiDataProcessors ||
        (window as any).dataProcessors ||
        {};

      const processorFn = globalProcessors[processor];
      if (typeof processorFn === 'function') {
        return await processorFn(data);
      }

      console.warn(
        `Data processor function "${processor}" not found in global scope`,
      );
      return data;
    };
  }

  // 如果是配置对象，创建对应的处理函数
  if (typeof processor === 'object' && processor !== null) {
    return async (data: any) => {
      const config = processor;

      // 根据配置类型处理数据
      if (config.type === 'formatOptions') {
        return await formatOptionsProcessor(data, config);
      }

      // 可以添加更多配置类型的处理
      console.warn(`Unknown processor config type: ${config.type}`);
      return data;
    };
  }

  // 默认返回原数据
  return async (data: any) => data;
}

/**
 * 格式化选项处理器
 * @param response API 响应数据
 * @param config 配置对象
 * @returns 处理后的数据
 */
async function formatOptionsProcessor(response: any, config: any) {
  let data = Array.isArray(response)
    ? response
    : response.items || response.data || response.list || [];

  // 过滤非活跃项 - 对于没有状态字段的数据，默认认为是活跃的
  if (config.filterInactive) {
    data = data.filter((item: any) => {
      // 如果没有状态字段，默认认为是活跃的
      if (
        !Object.prototype.hasOwnProperty.call(item, 'status') &&
        !Object.prototype.hasOwnProperty.call(item, 'is_active') &&
        !Object.prototype.hasOwnProperty.call(item, 'active') &&
        !Object.prototype.hasOwnProperty.call(item, 'enabled')
      ) {
        return true;
      }

      return (
        item.status === 'active' ||
        item.status === 1 ||
        item.is_active === true ||
        item.is_active === 1 ||
        item.active === true ||
        item.active === 1 ||
        item.enabled === true ||
        item.enabled === 1
      );
    });
  }

  // 过滤数据
  if (config.filterField && config.filterValue !== undefined) {
    data = data.filter(
      (item: any) => item[config.filterField] === config.filterValue,
    );
  }

  // 格式化标签
  if (config.labelFormat) {
    data = data.map((item: any) => ({
      ...item,
      label: config.labelFormat.replaceAll(
        /\{(\w+(?:\.\w+)*)\}/g,
        (_match: string, key: string) => {
          // 支持嵌套属性访问，如 {category.name}
          const keys = key.split('.');
          let value = item;
          for (const k of keys) {
            value = value?.[k];
            if (value === undefined || value === null) break;
          }
          return value || '';
        },
      ),
    }));
  }

  // 排序
  if (config.sortBy) {
    data.sort((a: any, b: any) => {
      const aVal = a[config.sortBy];
      const bVal = b[config.sortBy];
      if (config.sortOrder === 'desc') {
        return bVal > aVal ? 1 : bVal < aVal ? -1 : 0;
      }
      return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
    });
  }

  // 添加空选项
  if (config.addEmptyOption) {
    data.unshift({
      id: '',
      value: '',
      label: config.emptyOptionText || '请选择',
    });
  }

  return data;
}

/**
 * 创建分组标题的 schema
 * @param label 分组标题
 * @returns 分组标题 schema
 */
function createGroupDivider(label: string): VbenFormSchema {
  return {
    fieldName: `groupTitle_${label}`,
    component: 'GroupTitle',
    formItemClass: 'col-span-3',
    hideLabel: true,
    componentProps: {
      title: label,
    },
  } as any;
}

/**
 * 将后端搜索条件数据转换为前端表单 Schema
 * @param data 后端数据（新格式），支持分组结构或简单数组
 * @param options 转换选项
 * @param options.enableGrouping 是否启用分组功能，默认为 true
 * @param options.formMode 表单模式：'add' | 'edit' | undefined，用于控制字段的编辑权限
 * @returns 前端表单 Schema 数组
 */
export function transformBackendSearchToSchema(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  options: { enableGrouping?: boolean; formMode?: 'add' | 'edit' } = {},
): VbenFormSchema[] {
  const { enableGrouping = true, formMode } = options;
  const newSchema: VbenFormSchema[] = [];
  // 如果是简单数组格式
  if (Array.isArray(data)) {
    // 检查是否是分组数组格式（数组中的元素包含 label 和 dataItem）
    if (data.length > 0 && data[0] && 'dataItem' in data[0]) {
      // 处理直接的分组数组格式
      (data as GroupedSearchData[]).forEach((group) => {
        // 转换分组内的字段
        if (group.dataItem && Array.isArray(group.dataItem)) {
          const groupItems = group.dataItem
            .map((item) => transformSingleItem(item, formMode))
            .filter((item): item is VbenFormSchema => item !== null);

          // 只有当分组内有有效字段时，才添加分组标题和字段
          if (groupItems.length > 0) {
            // 添加分组标题（如果有 label 且启用分组）
            if (group.label && enableGrouping) {
              newSchema.push(createGroupDivider(group.label));
            }
            newSchema.push(...groupItems);
          }
        }
      });
      return newSchema;
    } else {
      // 处理简单数组格式
      const backendItems = data as BackendSearchItem[];
      const items = backendItems
        .map((item) => transformSingleItem(item, formMode))
        .filter((item): item is VbenFormSchema => item !== null);
      newSchema.push(...items);
    }
  }

  // 如果是包装在 schema 属性中的分组格式
  if ('schema' in data && Array.isArray(data.schema)) {
    (data as DirectGroupedSearchData).schema.forEach((group) => {
      // 转换分组内的字段
      if (group.dataItem && Array.isArray(group.dataItem)) {
        const groupItems = group.dataItem
          .map((item) => transformSingleItem(item, formMode))
          .filter((item): item is VbenFormSchema => item !== null);

        // 只有当分组内有有效字段时，才添加分组标题和字段
        if (groupItems.length > 0) {
          // 添加分组标题（如果有 label 且启用分组）
          if (group.label && enableGrouping) {
            newSchema.push(createGroupDivider(group.label));
          }
          newSchema.push(...groupItems);
        }
      }
    });
  }

  return newSchema;
}

/**
 * 为表单 API 设置初始化跟踪的包装函数
 * @param formApi 表单 API
 * @param schema 表单 schema
 */
export function setSchemaWithTracking(formApi: any, schema: any[]) {
  if (!formApi) {
    return;
  }

  // 设置 schema
  formApi.setState({ schema });
}

/**
 * 将后端搜索条件数据转换为前端表单 Schema（带分组）
 * @param data 后端数据
 * @param formMode 表单模式：'add' | 'edit' | undefined
 * @returns 前端表单 Schema 数组（包含分组标题）
 */
export function transformBackendSearchToSchemaWithGrouping(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  formMode?: 'add' | 'edit',
): VbenFormSchema[] {
  return transformBackendSearchToSchema(data, {
    enableGrouping: true,
    formMode,
  });
}

/**
 * 将后端搜索条件数据转换为前端表单 Schema（不分组）
 * @param data 后端数据
 * @param formMode 表单模式：'add' | 'edit' | undefined
 * @returns 前端表单 Schema 数组（不包含分组标题）
 */
export function transformBackendSearchToSchemaWithoutGrouping(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  formMode?: 'add' | 'edit',
): VbenFormSchema[] {
  return transformBackendSearchToSchema(data, {
    enableGrouping: false,
    formMode,
  });
}

/**
 * 检查值是否有效（不为 undefined、null、空字符串）
 */
function hasValue(value: any): boolean {
  return value !== undefined && value !== null && value !== '';
}

// 用于跟踪表单是否正在进行批量设置（回显数据）
const formBatchSettingMap = new Map<any, boolean>();

/**
 * 设置表单批量设置状态
 * @param formApi 表单API实例
 * @param isBatchSetting 是否正在批量设置
 */
export function setFormBatchSetting(formApi: any, isBatchSetting: boolean) {
  if (formApi) {
    formBatchSettingMap.set(formApi, isBatchSetting);

    // 如果设置为批量设置状态，5秒后自动清除（防止状态泄漏）
    if (isBatchSetting) {
      setTimeout(() => {
        formBatchSettingMap.delete(formApi);
      }, 5000);
    }
  }
}

/**
 * 检查表单是否正在批量设置
 * @param formApi 表单API实例
 * @returns 是否正在批量设置
 */
export function isFormBatchSetting(formApi: any): boolean {
  return formBatchSettingMap.get(formApi) === true;
}

/**
 * 转换 options 配置 - 使用统一的选项处理函数
 * @param options 选项配置
 * @param fieldName 字段名（用于错误提示）
 * @param fieldType 字段类型（用于错误提示）
 * @returns 标准化的选项数组
 */
function transformOptions(
  options: any,
  fieldName: string,
  fieldType: string,
): Array<{ disabled?: boolean; label: string; value: any }> {
  return normalizeOptions(options, {
    fieldName,
    fieldType,
    componentName: 'transformBackendSearchToSchema',
  });
}

/**
 * 深度清理对象中的 undefined 和 null 值
 * @param obj 要清理的对象
 * @returns 清理后的对象
 */
function cleanUndefinedValues<T extends Record<string, any>>(obj: T): T {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  const cleaned = {} as T;

  for (const [key, value] of Object.entries(obj)) {
    // 跳过 undefined 和 null 值
    if (value === undefined || value === null) {
      continue;
    }

    // 如果是数组，递归清理数组中的每个元素
    if (Array.isArray(value)) {
      const cleanedArray = value
        .map((item) =>
          typeof item === 'object' && item !== null
            ? cleanUndefinedValues(item)
            : item,
        )
        .filter((item) => item !== undefined && item !== null);

      if (cleanedArray.length > 0) {
        (cleaned as any)[key] = cleanedArray;
      }
    }
    // 如果是对象，递归清理
    else if (typeof value === 'object') {
      const cleanedObject = cleanUndefinedValues(value);
      // 只有当清理后的对象不为空时才添加
      if (Object.keys(cleanedObject).length > 0) {
        (cleaned as any)[key] = cleanedObject;
      }
    }
    // 其他类型的值直接添加
    else {
      (cleaned as any)[key] = value;
    }
  }

  return cleaned;
}

/**
 * 转换单个搜索条件
 * @param item 后端搜索条件（新格式）
 * @param formMode 表单模式：'add' | 'edit' | undefined（默认为 undefined，表示不区分模式）
 * @returns 前端表单 schema，如果字段应该被剔除则返回 null
 */
function transformSingleItem(
  item: BackendSearchItem,
  formMode?: 'add' | 'edit',
): null | VbenFormSchema {
  // 处理 ifShow 为 false 的情况：直接剔除该字段
  if (item.ifShow !== undefined) {
    if (typeof item.ifShow === 'boolean' && item.ifShow === false) {
      // ifShow 为 false 时，直接剔除该字段，不加入转换规则
      return null;
    }
    if (typeof item.ifShow === 'function') {
      // 如果是函数，可以在这里进行预判断（可选）
      // 注意：这里只是示例，实际项目中可能需要根据具体情况决定是否预判断
    }
  }

  const component = TYPE_TO_COMPONENT_MAP[item.type] || 'Input';
  // 创建 config 的副本，避免修改原始数据
  const config = { ...item.config };

  // 处理编辑权限控制
  if (config.editPermission && formMode) {
    const shouldDisable =
      (config.editPermission === 'add-only' && formMode === 'edit') ||
      (config.editPermission === 'edit-only' && formMode === 'add') ||
      config.editPermission === 'none';

    if (shouldDisable) {
      // 如果当前模式下不允许编辑，设置为禁用状态
      // 注意：这里不直接返回 null，而是设置 disabled，这样字段仍然显示但不可编辑
      config.disabled = true;
    }
  }

  // 基础 schema - 只包含有值的属性
  const schema: VbenFormSchema = {
    fieldName: item.field,
    label: item.title,
    component,
  };

  // 只有当属性有值时才添加到 schema 中
  if (hasValue(item.default)) {
    schema.defaultValue = item.default;
  }

  // 处理 formItemClass
  if (hasValue(config.formItemClass)) {
    schema.formItemClass = config.formItemClass;
  } else if (item.type === 'hidden') {
    // 为 hidden 类型添加默认的隐藏样式类
    schema.formItemClass = 'hidden';
  } else {
    // 为普通字段设置默认的宽度样式，适配响应式布局
    // 根据表单默认布局 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3' 设置合适的宽度
    // 小屏：全宽(1/1)，中屏：全宽(1/2)，大屏：全宽(1/3) - 这样每个字段都占满可用宽度
    schema.formItemClass = 'col-span-1';
  }

  // 构建组件属性 - 只包含有值的属性
  const componentProps: Record<string, any> = {};

  // 为所有组件统一添加 w-full 类，确保宽度一致性
  componentProps.class = 'w-full';

  // 添加自定义组件属性（如果有值）
  if (config.componentProps && Object.keys(config.componentProps).length > 0) {
    // 如果自定义属性中有 class，则合并而不是覆盖
    if (config.componentProps.class) {
      componentProps.class = `w-full ${config.componentProps.class}`;
    }
    // 添加其他自定义属性
    Object.assign(componentProps, {
      ...config.componentProps,
      class: componentProps.class, // 确保 class 不被覆盖
    });
  }

  // 根据类型设置特定属性
  switch (item.type) {
    case 'apiSelect':
    case 'apiselect': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      // 如果配置了搜索相关属性，默认启用搜索
      componentProps.showSearch = hasValue(config.showSearch)
        ? config.showSearch
        : !!(config.searchable || config.url);
      // 如果使用 API 搜索，禁用本地过滤
      componentProps.filterOption = hasValue(config.filterOption)
        ? config.filterOption
        : !config.url;

      // 多选模式：将 multiple: true 转换为 mode: 'multiple'
      if (config.multiple === true) {
        componentProps.mode = 'multiple';
      } else if (hasValue(config.mode)) {
        componentProps.mode = config.mode;
      }

      // 多选相关配置
      if (hasValue(config.maxTagCount)) {
        componentProps.maxTagCount = config.maxTagCount;
      }
      if (hasValue(config.maxTagTextLength)) {
        componentProps.maxTagTextLength = config.maxTagTextLength;
      }
      if (hasValue(config.maxTagPlaceholder)) {
        componentProps.maxTagPlaceholder = config.maxTagPlaceholder;
      }

      // 处理 API 相关配置
      if (hasValue(config.url) && config.url) {
        // 使用 url 而不是 api，支持搜索功能
        // 检查是否启用了搜索功能且不是禁用搜索请求
        if (
          (config.showSearch || config.searchable) &&
          config.disableSearchRequest !== true
        ) {
          // 创建支持搜索和分页的 API 函数
          const searchParamName =
            config.searchParamName || config.searchFieldName || 'search';

          // 使用支持 ApiSelect 滚动加载的 API 函数
          const finalPageSize =
            config.pagesize ||
            config.pageSize ||
            config.params?.pageSize ||
            config.params?.pagesize ||
            20;
          const apiConfig = {
            pageSize: finalPageSize,
            pageParamName: 'page',
            pageSizeParamName: 'pageSize',
            searchParamName,
          };

          componentProps.api = createApiSelectPaginatedFunction(
            config.url,
            config.params || {},
            apiConfig,
          );

          // 传递搜索字段名给组件
          if (hasValue(config.searchFieldName)) {
            componentProps.searchFieldName = config.searchFieldName;
          }
          if (hasValue(config.searchParamName)) {
            componentProps.searchParamName = config.searchParamName;
          }
        } else {
          // 普通的 API 函数，类似 listAction 格式
          componentProps.api = createListActionFunction(
            config.url,
            config.params || {},
          );
        }
      } else if (hasValue(config.api)) {
        // 兼容旧的 api 配置
        componentProps.api = config.api;
      }

      // API 相关配置 - 设置默认值
      // 根据实际 API 返回的数据结构，默认使用 name 和 id 字段
      // 如果使用了 afterFetch 配置对象进行格式化，则使用 label 字段
      const hasAfterFetchFormatting =
        config.afterFetch &&
        typeof config.afterFetch === 'object' &&
        (config.afterFetch as any).type === 'formatOptions' &&
        (config.afterFetch as any).labelFormat;

      // 当使用格式化配置时，强制使用 label 字段，忽略后端的 labelField 设置
      componentProps.labelField = hasAfterFetchFormatting
        ? 'label'
        : config.labelField || 'name';
      componentProps.valueField = config.valueField || 'id';

      // 只有明确配置了 resultField 才设置，否则让组件使用默认行为
      if (hasValue(config.resultField)) {
        componentProps.resultField = config.resultField;
      }

      // 自定义标签格式化函数
      if (
        hasValue(config.labelFormatter) &&
        typeof config.labelFormatter === 'function'
      ) {
        componentProps.labelFormatter = config.labelFormatter;
      }

      // 确保在编辑模式下立即加载数据，以便正确显示标签
      // 如果后端没有传递 immediate 参数，默认设置为 true
      componentProps.immediate = hasValue(config.immediate)
        ? config.immediate
        : true;
      if (hasValue(config.alwaysLoad)) {
        componentProps.alwaysLoad = config.alwaysLoad;
      }
      if (hasValue(config.autoSelect)) {
        componentProps.autoSelect = config.autoSelect;
      }
      if (hasValue(config.beforeFetch)) {
        componentProps.beforeFetch = createDataProcessor(
          config.beforeFetch,
          'beforeFetch',
        );
      }
      if (hasValue(config.afterFetch)) {
        componentProps.afterFetch = createDataProcessor(
          config.afterFetch,
          'afterFetch',
        );
      }

      // 回显数据时的参数字段配置
      if (hasValue(config.returnParamsField)) {
        // 直接将 returnParamsField 赋值给组件属性，由组件内部处理
        componentProps.returnParamsField = config.returnParamsField;
      }

      break;
    }
    case 'apiTree': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      componentProps.showSearch = hasValue(config.showSearch)
        ? config.showSearch
        : false;

      // 多选模式：将 multiple: true 转换为 multiple: true（TreeSelect 使用 multiple 属性）
      if (config.multiple === true) {
        componentProps.multiple = true;
      } else if (hasValue(config.multiple)) {
        componentProps.multiple = config.multiple;
      }

      // 检查是否为多选模式（multiple 或 treeCheckable）
      const isMultipleMode = componentProps.multiple || config.treeCheckable;

      // 如果是多选模式且有默认值，确保默认值是数组格式
      if (isMultipleMode && hasValue(item.default)) {
        if (Array.isArray(item.default)) {
          schema.defaultValue = item.default;
        } else {
          // 如果默认值不是数组，转换为数组
          schema.defaultValue =
            item.default !== null && item.default !== undefined
              ? [item.default]
              : [];
        }
      } else if (hasValue(item.default)) {
        // 单选模式，直接使用默认值
        schema.defaultValue = item.default;
      }

      // 多选相关配置
      if (hasValue(config.maxTagCount)) {
        componentProps.maxTagCount = config.maxTagCount;
      }
      if (hasValue(config.maxTagTextLength)) {
        componentProps.maxTagTextLength = config.maxTagTextLength;
      }
      if (hasValue(config.maxTagPlaceholder)) {
        componentProps.maxTagPlaceholder = config.maxTagPlaceholder;
      }

      // 树形组件特有配置
      if (hasValue(config.treeCheckable)) {
        componentProps.treeCheckable = config.treeCheckable;
      }
      if (hasValue(config.treeCheckStrictly)) {
        componentProps.treeCheckStrictly = config.treeCheckStrictly;
      }

      // 处理 API 相关配置
      if (hasValue(config.url) && config.url) {
        // 检查是否需要高级功能（搜索或分页）
        const needsAdvancedFeatures =
          (config.showSearch || config.searchable || config.pagination) &&
          config.disableSearchRequest !== true;

        if (needsAdvancedFeatures) {
          // 使用搜索 API 函数，类似 listAction 格式，并添加分页相关属性
          const searchParamName =
            config.searchParamName || config.searchFieldName || 'search';
          componentProps.api = createSearchableListActionFunction(
            config.url,
            config.params || {},
            searchParamName,
          );

          // 传递搜索字段名给组件
          if (hasValue(config.searchFieldName)) {
            componentProps.searchFieldName = config.searchFieldName;
          }
          if (hasValue(config.searchParamName)) {
            componentProps.searchParamName = config.searchParamName;
          }

          // 如果启用分页，添加分页相关属性
          if (config.pagination) {
            componentProps.pagination = true;
            componentProps.pageSize = config.pageSize || 20;
            componentProps.loadMore = config.loadMore !== false;
            componentProps.pageParamName = config.pageParamName || 'page';
            componentProps.pageSizeParamName =
              config.pageSizeParamName || 'pageSize';
          }
        } else {
          // 使用基础 API 函数，类似 listAction 格式
          componentProps.api = createListActionFunction(
            config.url,
            config.params || {},
          );
        }
      } else if (hasValue(config.api)) {
        // 兼容旧的 api 配置
        componentProps.api = config.api;
      }

      // API 相关配置 - 设置默认值
      // 如果使用了 afterFetch 配置对象进行格式化，则使用 label 字段
      const hasAfterFetchFormatting =
        config.afterFetch &&
        typeof config.afterFetch === 'object' &&
        (config.afterFetch as any).type === 'formatOptions' &&
        (config.afterFetch as any).labelFormat;

      // 当使用格式化配置时，强制使用 label 字段，忽略后端的 labelField 设置
      componentProps.labelField = hasAfterFetchFormatting
        ? 'label'
        : config.labelField || 'label';
      componentProps.valueField = config.valueField || 'value';
      componentProps.childrenField = config.childrenField || 'children';

      // 只有明确配置了 resultField 才设置，否则让组件使用默认行为
      if (hasValue(config.resultField)) {
        componentProps.resultField = config.resultField;
      }

      // 确保在编辑模式下立即加载数据，以便正确显示标签
      // 如果后端没有传递 immediate 参数，默认设置为 true
      componentProps.immediate = hasValue(config.immediate)
        ? config.immediate
        : true;
      if (hasValue(config.alwaysLoad)) {
        componentProps.alwaysLoad = config.alwaysLoad;
      }
      if (hasValue(config.beforeFetch)) {
        componentProps.beforeFetch = createDataProcessor(
          config.beforeFetch,
          'beforeFetch',
        );
      }
      if (hasValue(config.afterFetch)) {
        componentProps.afterFetch = createDataProcessor(
          config.afterFetch,
          'afterFetch',
        );
      }

      // 树形组件特有配置
      if (hasValue(config.treeDefaultExpandAll)) {
        componentProps.treeDefaultExpandAll = config.treeDefaultExpandAll;
      }

      // 默认添加 filterTreeNode 函数，如果没有提供的话
      componentProps.filterTreeNode = hasValue(config.filterTreeNode)
        ? config.filterTreeNode
        : defaultFilterTreeNode;

      // 回显数据时的参数字段配置
      if (hasValue(config.returnParamsField)) {
        // 直接将 returnParamsField 赋值给组件属性，由组件内部处理
        componentProps.returnParamsField = config.returnParamsField;
      }
      break;
    }

    case 'checkbox':
    case 'radio': {
      // 设置选项数据 - 支持数组和对象两种格式
      componentProps.options = transformOptions(
        config.options,
        item.field,
        item.type,
      );

      // radio 特有配置
      if (item.type === 'radio') {
        // 设置默认的按钮样式
        componentProps.optionType = hasValue(config.optionType)
          ? config.optionType
          : 'default'; // 可选值: 'default' | 'button'

        // 设置按钮样式（当 optionType 为 'button' 时）
        if (config.optionType === 'button') {
          componentProps.buttonStyle = hasValue(config.buttonStyle)
            ? config.buttonStyle
            : 'outline'; // 可选值: 'outline' | 'solid'
        }
      }
      break;
    }

    case 'date': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      break;
    }

    case 'dateRange': {
      componentProps.placeholder = config.placeholder || [
        '开始日期',
        '结束日期',
      ];
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      break;
    }

    case 'edittable': {
      // edittable 类型转换为 EditTable 组件
      schema.component = 'EditTable';

      // edittable 类型独占一行，在所有屏幕尺寸下都占满整行
      schema.formItemClass =
        'col-span-1 sm:col-span-2 md:col-span-3 items-baseline';

      // 将 config 中的所有配置作为 params 传递给 EditTable 组件
      componentProps.params = {
        columns: (config as any).columns || [],
        tabList: (config as any).tabList || [],
        // 将 config 中的所有内容都作为 params 传递
        ...config,
      };

      // 处理外部联动配置
      if ((config as any).externalLinkage) {
        componentProps.params.externalLinkage = (config as any).externalLinkage;

        console.log(
          `[edittable外部联动] 字段 ${item.field} 检测到外部联动配置:`,
          {
            field: item.field,
            externalLinkage: (config as any).externalLinkage,
          },
        );
      }

      // 设置默认值为 tabList 数据
      if ((config as any).tabList && Array.isArray((config as any).tabList)) {
        schema.defaultValue = (config as any).tabList;
      }

      break;
    }

    case 'hidden': {
      // 隐藏字段：不显示在界面上，但会包含在表单提交中
      componentProps.style = { display: 'none' };
      break;
    }
    case 'input':
    case 'password':
    case 'text': {
      break;
    }

    case 'mentions': {
      componentProps.placeholder = config.placeholder || `请输入${item.title}`;
      // 设置选项数据 - 支持数组和对象两种格式
      if (hasValue(config.options)) {
        componentProps.options = transformOptions(
          config.options,
          item.field,
          item.type,
        );
      }
      break;
    }

    case 'number': {
      componentProps.placeholder = config.placeholder || `请输入${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      if (hasValue(config.min)) componentProps.min = config.min;
      if (hasValue(config.max)) componentProps.max = config.max;
      if (hasValue(config.precision))
        componentProps.precision = config.precision;
      break;
    }

    case 'rate': {
      // Rate 组件通常不需要特殊配置
      break;
    }

    case 'select': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      componentProps.showSearch = hasValue(config.showSearch)
        ? config.showSearch
        : false;
      componentProps.filterOption = hasValue(config.filterOption)
        ? config.filterOption
        : true;

      // 多选模式：将 multiple: true 转换为 mode: 'multiple'
      if (config.multiple === true) {
        componentProps.mode = 'multiple';
      } else if (hasValue(config.mode)) {
        componentProps.mode = config.mode;
      }

      // 多选相关配置
      if (hasValue(config.maxTagCount)) {
        componentProps.maxTagCount = config.maxTagCount;
      }
      if (hasValue(config.maxTagTextLength)) {
        componentProps.maxTagTextLength = config.maxTagTextLength;
      }
      if (hasValue(config.maxTagPlaceholder)) {
        componentProps.maxTagPlaceholder = config.maxTagPlaceholder;
      }

      // 设置选项数据 - 支持数组和对象两种格式
      if (hasValue(config.options)) {
        componentProps.options = transformOptions(
          config.options,
          item.field,
          item.type,
        );
      }
      break;
    }

    case 'switch': {
      // Switch 组件通常不需要 placeholder
      break;
    }

    case 'textarea': {
      componentProps.placeholder = config.placeholder || `请输入${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      break;
    }

    case 'time': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      break;
    }

    case 'tree': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      componentProps.showSearch = hasValue(config.showSearch)
        ? config.showSearch
        : false;

      // 多选模式：将 multiple: true 转换为 multiple: true（TreeSelect 使用 multiple 属性）
      if (config.multiple === true) {
        componentProps.multiple = true;
      } else if (hasValue(config.multiple)) {
        componentProps.multiple = config.multiple;
      }

      // 多选相关配置
      if (hasValue(config.maxTagCount)) {
        componentProps.maxTagCount = config.maxTagCount;
      }
      if (hasValue(config.maxTagTextLength)) {
        componentProps.maxTagTextLength = config.maxTagTextLength;
      }
      if (hasValue(config.maxTagPlaceholder)) {
        componentProps.maxTagPlaceholder = config.maxTagPlaceholder;
      }

      // 树形组件特有配置
      if (hasValue(config.treeCheckable)) {
        componentProps.treeCheckable = config.treeCheckable;
      }
      if (hasValue(config.treeCheckStrictly)) {
        componentProps.treeCheckStrictly = config.treeCheckStrictly;
      }
      if (hasValue(config.treeDefaultExpandAll)) {
        componentProps.treeDefaultExpandAll = config.treeDefaultExpandAll;
      }

      if (hasValue(config.treeData) && Array.isArray(config.treeData)) {
        componentProps.treeData = config.treeData;
      }
      break;
    }
    case 'Upload': {
      // Upload 组件的默认配置
      const uploadConfig = config as any;
      componentProps.accept = uploadConfig.accept || '.png,.jpg,.jpeg';
      componentProps.action = 'oss/putFile';
      componentProps.maxCount = uploadConfig.maxCount || 1;
      componentProps.multiple = hasValue(uploadConfig.multiple)
        ? uploadConfig.multiple
        : false;
      componentProps.showUploadList = hasValue(uploadConfig.showUploadList)
        ? uploadConfig.showUploadList
        : true;
      componentProps.listType = uploadConfig.listType || 'picture-card';

      // 处理默认值：支持字符串、字符串数组和逗号分隔字符串
      if (hasValue(item.default)) {
        if (Array.isArray(item.default)) {
          // 如果是数组，转换每个元素为文件对象
          schema.defaultValue = item.default
            .map((url: string, index: number) => stringToFileObject(url, index))
            .filter(Boolean);
        } else if (typeof item.default === 'string') {
          if (componentProps.multiple && item.default.includes(',')) {
            // 多文件：将逗号分隔的字符串转换为文件对象数组
            schema.defaultValue = stringToFileList(item.default);
          } else {
            // 单文件：将字符串转换为单个文件对象数组
            const fileObj = stringToFileObject(item.default);
            schema.defaultValue = fileObj ? [fileObj] : [];
          }
        } else {
          schema.defaultValue = [];
        }
      } else {
        schema.defaultValue = [];
      }

      // 简化的自定义上传处理（类似 UploadModal.vue）
      componentProps.customRequest = async (options: any) => {
        const { file, onSuccess, onError, onProgress } = options;

        try {
          // 模拟上传进度
          onProgress?.({ percent: 0 });

          // 使用 baseRequestClient 的 upload 方法
          const response = await baseRequestClient.upload('oss/putFile', {
            file,
          });

          // 模拟上传进度完成
          onProgress?.({ percent: 100 });

          // 直接使用返回的字符串作为响应
          onSuccess?.(response, file);
        } catch (error) {
          onError?.(error);
        }
      };

      // 添加调试用的 onChange 处理
      componentProps.onChange = (info: any) => {
        console.log(`[Upload ${item.field}] 文件列表变化:`, {
          totalFiles: info.fileList?.length || 0,
          fileList: info.fileList,
          file: info.file,
          event: info.event,
        });

        // 检查每个文件的状态
        if (info.fileList) {
          info.fileList.forEach((file: any, index: number) => {
            console.log(`[Upload ${item.field}] 文件 ${index}:`, {
              name: file.name,
              status: file.status,
              response: file.response,
              uid: file.uid,
            });
          });
        }
      };

      // 如果后端配置了自定义上传函数，则使用后端配置
      if (uploadConfig.customRequest) {
        componentProps.customRequest = uploadConfig.customRequest;
      }

      // 设置渲染内容（上传按钮的文本）
      if (uploadConfig.uploadText || item.title) {
        schema.renderComponentContent = () => {
          return {
            default: () => uploadConfig.uploadText || `上传${item.title}`,
          };
        };
      }

      break;
    }

    default: {
      // 默认情况下的通用属性
      if (hasValue(config.placeholder)) {
        componentProps.placeholder = config.placeholder;
      }
      if (hasValue(config.allowClear)) {
        componentProps.allowClear = config.allowClear;
      }
      break;
    }
  }

  // 添加通用属性
  // disabled 属性需要特殊处理，因为 false 也是有效值
  // 优先使用外层的 disabled，如果没有则使用 config.disabled
  if (item.disabled !== undefined && item.disabled !== null) {
    componentProps.disabled = item.disabled;
  } else if (config.disabled !== undefined && config.disabled !== null) {
    componentProps.disabled = config.disabled;
  }

  if (hasValue(config.suffix)) {
    componentProps.suffix = config.suffix;
  }

  if (hasValue(config.prefix)) {
    componentProps.prefix = config.prefix;
  }

  // 只有当 componentProps 有内容时才添加到 schema 中
  if (Object.keys(componentProps).length > 0) {
    schema.componentProps = componentProps;
  }

  // 添加验证规则
  if (item.required || config.ruleType) {
    schema.rules =
      item.type === 'select' ||
      item.type === 'tree' ||
      item.type === 'radio' ||
      item.type === 'date' ||
      item.type === 'dateRange'
        ? config.ruleType || 'selectRequired'
        : config.ruleType || 'required';
  }

  // 处理联动赋值配置 - 使用事件系统避免与回显数据冲突
  if (
    config.linkageAssignment &&
    (item.type === 'select' ||
      item.type === 'apiSelect' ||
      item.type === 'apiselect' ||
      item.type === 'tree' ||
      item.type === 'apiTree' ||
      item.type === 'radio')
  ) {
    console.warn(`[联动赋值配置] 字段 ${item.field} 检测到联动赋值配置:`, {
      field: item.field,
      type: item.type,
      linkageAssignment: config.linkageAssignment,
    });
    // 添加 onChange 事件处理，使用延迟执行避免回显数据触发联动
    const originalComponentProps = schema.componentProps;
    schema.componentProps = (values: Record<string, any>, formApi?: any) => {
      const baseProps =
        typeof originalComponentProps === 'function'
          ? originalComponentProps(values, formApi)
          : originalComponentProps || {};

      return {
        ...baseProps,
        onChange: (value: any, selectedOption: any, ...args: any[]) => {
          // 更严格的用户交互检测
          // 只有当用户真正从UI中选择时，selectedOption才会有值
          // formApi.setFieldValue() 调用时，selectedOption通常为undefined
          const hasSelectedOption =
            selectedOption !== undefined && selectedOption !== null;

          // 额外检查：如果是批量设置状态，也跳过联动
          const isBatchSetting = isFormBatchSetting(formApi);

          const isRealUserInteraction = hasSelectedOption && !isBatchSetting;

          console.warn('[联动赋值] onChange 触发:', {
            field: item.field,
            value,
            hasSelectedOption,
            isBatchSetting,
            isRealUserInteraction,
            note: 'formApi.setFieldValue()调用不应触发联动',
          });

          // 只有在真正的用户交互时才执行联动逻辑
          if (isRealUserInteraction) {
            console.warn('[联动赋值] 执行用户交互联动:', {
              field: item.field,
              value,
            });

            const targetFields = config.linkageAssignment?.targetFields;
            if (targetFields) {
              targetFields.forEach((targetField: any) => {
                try {
                  if (value === undefined || value === null || value === '') {
                    if (
                      targetField.clearOnEmpty !== false &&
                      formApi?.setFieldValue
                    ) {
                      formApi.setFieldValue(targetField.field, undefined);
                    }
                  } else {
                    let targetValue;
                    if (typeof targetField.valueMapping === 'function') {
                      targetValue = targetField.valueMapping(
                        value,
                        selectedOption,
                      );
                    } else if (typeof targetField.valueMapping === 'string') {
                      targetValue =
                        selectedOption?.[targetField.valueMapping] || null;
                    } else if (
                      typeof targetField.valueMapping === 'object' &&
                      targetField.valueMapping !== null
                    ) {
                      targetValue =
                        targetField.valueMapping[value] ||
                        targetField.valueMapping.default ||
                        null;
                    } else {
                      targetValue = targetField.valueMapping;
                    }

                    if (formApi?.setFieldValue) {
                      formApi.setFieldValue(targetField.field, targetValue);

                      console.warn('[联动赋值] 设置目标字段值:', {
                        targetField: targetField.field,
                        targetValue,
                        note: '目标字段通过returnParamsField自动处理API请求',
                      });
                    }
                  }
                } catch (error) {
                  console.warn(
                    `联动赋值失败: ${item.field} -> ${targetField.field}`,
                    error,
                  );
                }
              });
            }
          } else {
            console.warn('[联动赋值] 跳过程序设置的联动:', {
              field: item.field,
              value,
            });
          }

          // 调用原有的 onChange 事件
          if (baseProps.onChange) {
            baseProps.onChange(value, selectedOption, ...args);
          }
        },
      };
    };
  }

  // 处理计算配置（config.calculation）
  if (config.calculation) {
    console.warn(`[计算配置] 字段 ${item.field} 检测到计算配置:`, {
      field: item.field,
      type: item.type,
      calculation: config.calculation,
    });

    // 过滤触发字段，确保不包含计算字段本身（防止递归）
    const filteredTriggerFields = (
      config.calculation.triggerFields || []
    ).filter((triggerField: string) => triggerField !== item.field);

    if (filteredTriggerFields.length === 0) {
      console.warn(
        `[计算配置] 字段 ${item.field} 的触发字段为空或只包含自身，跳过计算配置`,
      );
    } else {
      // 将 config.calculation 转换为联动配置
      const calculationLinkage = {
        triggerFields: filteredTriggerFields,
        rules: {
          calculation: {
            ...config.calculation,
            triggerFields: filteredTriggerFields,
          },
        },
      };

      const calculationDependencies =
        LinkageRuleConverter.convertLinkageToVbenDependencies(
          calculationLinkage,
        );

      if (calculationDependencies) {
        // 包装 trigger 函数以传递字段名
        if (calculationDependencies.trigger) {
          const originalTrigger = calculationDependencies.trigger;
          calculationDependencies.trigger = (
            values: Record<string, any>,
            formApi?: any,
          ) => {
            if (formApi) {
              formApi.fieldName = item.field;
            }
            return originalTrigger(values, formApi);
          };
        }

        schema.dependencies = calculationDependencies;
      }
    }
  }

  // 处理联动配置 - 支持从 config.linkage 或顶级 linkage 读取
  const linkageConfig = config.linkage || item.linkage;
  if (linkageConfig) {
    console.warn(`[普通联动配置] 字段 ${item.field} 检测到普通联动配置:`, {
      field: item.field,
      type: item.type,
      linkageConfig,
      source: config.linkage ? 'config.linkage' : 'item.linkage',
    });

    const dependencies =
      LinkageRuleConverter.convertLinkageToVbenDependencies(linkageConfig);

    if (dependencies) {
      // 如果有 trigger，包装它以传递字段名
      if (dependencies.trigger) {
        const originalTrigger = dependencies.trigger;
        dependencies.trigger = (values: Record<string, any>, formApi?: any) => {
          // 为 formApi 添加字段名信息
          if (formApi) {
            formApi.fieldName = item.field;
          }
          return originalTrigger(values, formApi);
        };
      }

      // 如果有 componentProps，包装它以传递字段名
      if (dependencies.componentProps) {
        const originalComponentProps = dependencies.componentProps;
        dependencies.componentProps = (
          values: Record<string, any>,
          formApi?: any,
        ) => {
          // 为 formApi 添加字段名信息
          if (formApi) {
            formApi.fieldName = item.field;
          }
          return originalComponentProps(values, formApi);
        };
      }

      schema.dependencies = dependencies;
    }
  }

  // 清理 undefined 和 null 值
  const finalSchema = cleanUndefinedValues(schema);

  return finalSchema;
}

/**
 * 快速转换函数，直接返回可用的 schema
 * @param backendItems 后端搜索条件数组
 * @param formMode 表单模式：'add' | 'edit' | undefined
 * @returns 转换后的 schema 数组
 */
export function quickTransformSearch(
  backendItems: BackendSearchItem[],
  formMode?: 'add' | 'edit',
): VbenFormSchema[] {
  return transformBackendSearchToSchema(backendItems, { formMode });
}
