import { defineConfig } from '@vben/vite-config';

import { ProxyManager } from './proxy-manager';

export default defineConfig(async () => {
  // 加载环境变量
  const env = process.env as Record<string, string>;

  // 调试：输出关键环境变量
  console.warn('[Vite配置] 关键环境变量:', {
    VITE_PROXY_TARGET: env.VITE_PROXY_TARGET || 'undefined',
    VITE_PROXY_REWRITE_TO: env.VITE_PROXY_REWRITE_TO || 'undefined',
    NODE_ENV: env.NODE_ENV || 'undefined',
  });

  const proxyConfig = ProxyManager.getProxyConfig(env);
  console.warn('[Vite配置] 生成的代理配置:', proxyConfig);

  return {
    application: {},
    resolve: {
      alias: {
        '#': '/src', // 假设 # 指向 src 目录
      },
    },
    vite: {
      server: {
        proxy: proxyConfig,
      },
    },
  };
});
