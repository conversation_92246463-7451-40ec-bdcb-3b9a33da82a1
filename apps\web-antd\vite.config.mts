import { defineConfig } from '@vben/vite-config';

/**
 * 代理配置管理器
 * 支持通过环境变量配置多个代理环境
 */
class ProxyManager {
  private static readonly PROXY_CONFIGS = {
    // 本地开发环境
    local: {
      target: 'http://127.0.0.1:8000',
      changeOrigin: true,
      rewrite: (path: string) => path.replace(/^\/api/, '/apiv2'),
      ws: true,
    },
    // 测试环境
    test: {
      target: 'https://erp.gbuilderchina.com/testapiv2/',
      changeOrigin: true,
      rewrite: (path: string) => path.replace(/^\/api/, ''),
      ws: true,
    },
    // 预发布环境
    staging: {
      target: 'https://erp-staging.gbuilderchina.com/apiv2/',
      changeOrigin: true,
      rewrite: (path: string) => path.replace(/^\/api/, ''),
      ws: true,
    },
    // 生产环境
    production: {
      target: 'https://erp.gbuilderchina.com/apiv2/',
      changeOrigin: true,
      rewrite: (path: string) => path.replace(/^\/api/, ''),
      ws: true,
    },
  };

  /**
   * 获取所有可用的环境列表
   */
  static getAvailableEnvironments() {
    return Object.keys(this.PROXY_CONFIGS);
  }

  /**
   * 获取代理配置
   * @param env 环境变量对象
   * @returns 代理配置对象
   */
  static getProxyConfig(env: Record<string, string>) {
    // 从环境变量获取代理环境，默认为 local
    const proxyEnv = env.VITE_PROXY_ENV || 'local';

    // 支持自定义代理目标
    const customTarget = env.VITE_PROXY_TARGET;
    const customRewrite = env.VITE_PROXY_REWRITE;

    console.log(`[代理配置] 当前环境: ${proxyEnv}`);

    // 如果有自定义配置，优先使用自定义配置
    if (customTarget) {
      console.log(`[代理配置] 使用自定义目标: ${customTarget}`);
      return {
        '/api': {
          target: customTarget,
          changeOrigin: true,
          rewrite: customRewrite
            ? new Function('path', `return ${customRewrite}`)
            : (path: string) => path.replace(/^\/api/, '/apiv2'),
          ws: true,
        },
      };
    }

    // 使用预定义配置
    const config =
      this.PROXY_CONFIGS[proxyEnv as keyof typeof this.PROXY_CONFIGS];
    if (!config) {
      console.warn(
        `[代理配置] 未找到环境 "${proxyEnv}" 的配置，使用默认 local 配置`,
      );
      return {
        '/api': this.PROXY_CONFIGS.local,
      };
    }

    console.log(`[代理配置] 使用预定义配置:`, config);
    return {
      '/api': config,
    };
  }
}

export default defineConfig(async () => {
  return {
    application: {},
    resolve: {
      alias: {
        '#': '/src', // 假设 # 指向 src 目录
      },
    },
    vite: {
      server: {
        proxy: {
          // '/api': {
          //   changeOrigin: true,
          //   rewrite: (path) => path.replace(/^\/api/, ''),
          //   // mock代理目标地址
          //   target: 'https://erp.gbuilderchina.com/testapiv2/',
          //   ws: true,
          // },
          '/api': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, '/apiv2'),
            // mock代理目标地址
            // target: 'https://erp.gbuilderchina.com/testapiv2/',
            target: 'http://127.0.0.1:8000',
            ws: true,
          },
        },
      },
    },
  };
});
