import { defineConfig } from '@vben/vite-config';

import { ProxyManager } from './proxy-manager';

export default defineConfig(async () => {
  // 加载环境变量
  const env = process.env as Record<string, string>;

  return {
    application: {},
    resolve: {
      alias: {
        '#': '/src', // 假设 # 指向 src 目录
      },
    },
    vite: {
      server: {
        proxy: ProxyManager.getProxyConfig(env),
      },
    },
  };
});
