import { defineConfig } from '@vben/vite-config';

/**
 * 代理配置管理器
 * 完全基于环境变量的代理配置
 */
const ProxyManager = {
  /**
   * 从环境变量获取代理配置
   * @param env 环境变量对象
   * @returns 代理配置对象
   */
  getProxyConfig(env: Record<string, string>) {
    const proxyConfigs: Record<string, any> = {};

    // 获取基础配置
    const target = env.VITE_PROXY_TARGET;
    const pathPrefix = env.VITE_PROXY_PATH_PREFIX || '/api';
    const rewriteFrom = env.VITE_PROXY_REWRITE_FROM || '^/api';
    const rewriteTo = env.VITE_PROXY_REWRITE_TO || '/apiv2';
    const changeOrigin = env.VITE_PROXY_CHANGE_ORIGIN !== 'false'; // 默认为 true
    const ws = env.VITE_PROXY_WS !== 'false'; // 默认为 true

    // 如果没有配置目标地址，返回空配置
    if (!target) {
      return {};
    }

    // 基础代理配置
    proxyConfigs[pathPrefix] = {
      target,
      changeOrigin,
      ws,
      rewrite: (path: string) => {
        const regex = new RegExp(rewriteFrom);
        return path.replace(regex, rewriteTo);
      },
    };

    // 支持多个代理配置
    // 格式：VITE_PROXY_ADDITIONAL_1_TARGET=http://localhost:3000
    //      VITE_PROXY_ADDITIONAL_1_PREFIX=/upload
    //      VITE_PROXY_ADDITIONAL_1_REWRITE_FROM=^/upload
    //      VITE_PROXY_ADDITIONAL_1_REWRITE_TO=/files
    let index = 1;
    while (env[`VITE_PROXY_ADDITIONAL_${index}_TARGET`]) {
      const additionalTarget = env[`VITE_PROXY_ADDITIONAL_${index}_TARGET`];
      const additionalPrefix =
        env[`VITE_PROXY_ADDITIONAL_${index}_PREFIX`] || `/api${index}`;
      const additionalRewriteFrom =
        env[`VITE_PROXY_ADDITIONAL_${index}_REWRITE_FROM`] || `^/api${index}`;
      const additionalRewriteTo =
        env[`VITE_PROXY_ADDITIONAL_${index}_REWRITE_TO`] || '';
      const additionalChangeOrigin =
        env[`VITE_PROXY_ADDITIONAL_${index}_CHANGE_ORIGIN`] !== 'false';
      const additionalWs = env[`VITE_PROXY_ADDITIONAL_${index}_WS`] !== 'false';

      proxyConfigs[additionalPrefix] = {
        target: additionalTarget,
        changeOrigin: additionalChangeOrigin,
        ws: additionalWs,
        rewrite: (path: string) => {
          const regex = new RegExp(additionalRewriteFrom);
          return path.replace(regex, additionalRewriteTo);
        },
      };

      console.log(`[代理配置] 添加额外代理 ${index}:`, {
        prefix: additionalPrefix,
        target: additionalTarget,
        rewriteFrom: additionalRewriteFrom,
        rewriteTo: additionalRewriteTo,
      });

      index++;
    }

    console.log(`[代理配置] 最终配置:`, proxyConfigs);
    return proxyConfigs;
  },
};

export default defineConfig(async () => {
  // 加载环境变量
  const env = process.env as Record<string, string>;

  return {
    application: {},
    resolve: {
      alias: {
        '#': '/src', // 假设 # 指向 src 目录
      },
    },
    vite: {
      server: {
        proxy: ProxyManager.getProxyConfig(env),
      },
    },
  };
});
