import { defineConfig } from '@vben/vite-config';

import { loadEnv } from 'vite';

import { ProxyManager } from './proxy-manager';

export default defineConfig(async (config) => {
  const mode = config?.mode || 'development';

  // 正确加载环境变量
  const env = loadEnv(mode, '.', '');

  const proxyConfig = ProxyManager.getProxyConfig(env);

  return {
    application: {},
    vite: {
      resolve: {
        alias: {
          '#': '/src', // 假设 # 指向 src 目录
        },
      },
      server: {
        proxy: proxyConfig,
      },
    },
  };
});
