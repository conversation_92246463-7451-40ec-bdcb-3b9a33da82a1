# 后端环境切换指南

本项目支持在本地后端和远程后端之间快速切换，并且可以配置跳过特定的 API 接口。

## 🔧 配置文件说明

### 环境配置文件

- `.env.local` - 当前使用的环境配置
- `.env.local.backend` - 本地后端配置模板
- `.env.local.remote` - 远程后端配置（自动生成）

### 关键环境变量

- `VITE_PROXY_TARGET` - 代理目标地址
- `VITE_SKIP_TOKEN_REFRESH` - 是否跳过 token refresh 接口

## 🚀 使用方法

### 1. 切换到本地后端

```bash
# 方法一：使用 npm 脚本
pnpm backend:local

# 方法二：直接运行脚本
node switch-backend.js local

# 方法三：切换并启动开发服务器
pnpm dev:backend
```

### 2. 切换到远程后端

```bash
# 方法一：使用 npm 脚本
pnpm backend:remote

# 方法二：直接运行脚本
node switch-backend.js remote
```

### 3. 查看当前配置

```bash
pnpm backend:switch
```

## ⚙️ 本地后端配置

编辑 `.env.local.backend` 文件来配置本地后端：

```env
# 本地后端地址
VITE_PROXY_TARGET=http://127.0.0.1:8000

# 跳过 token refresh 接口
VITE_SKIP_TOKEN_REFRESH=true
```

## 🔍 接口跳过逻辑

当使用本地后端时，系统会自动跳过 `user/token/refresh` 接口：

1. **优先级判断**：首先检查 `VITE_SKIP_TOKEN_REFRESH` 环境变量
2. **自动判断**：如果未明确配置，会根据 `VITE_PROXY_TARGET` 是否包含本地地址来判断
3. **跳过行为**：返回模拟的成功响应 `{ data: 'skipped', status: 200 }`

## 📝 注意事项

1. **重启服务器**：切换后端配置后需要重启开发服务器
2. **配置文件**：`.env.local.backend` 需要根据实际本地后端地址进行配置
3. **版本控制**：`.env.local*` 文件不会被提交到 Git

## 🛠️ 自定义配置

如需添加更多跳过的接口，可以在 `src/api/core/auth.ts` 中修改相关逻辑。
