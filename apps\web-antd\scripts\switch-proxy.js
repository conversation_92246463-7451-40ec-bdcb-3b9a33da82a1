#!/usr/bin/env node

/**
 * 代理环境切换脚本
 * 用法：node scripts/switch-proxy.js [环境名称]
 * 示例：node scripts/switch-proxy.js test
 */

const fs = require('fs');
const path = require('path');

// 环境配置预设
const ENV_PRESETS = {
  local: {
    VITE_PROXY_TARGET: 'http://127.0.0.1:8000',
    VITE_PROXY_REWRITE_FROM: '^/api',
    VITE_PROXY_REWRITE_TO: '/apiv2',
  },
  test: {
    VITE_PROXY_TARGET: 'https://erp.gbuilderchina.com/testapiv2/',
    VITE_PROXY_REWRITE_FROM: '^/api',
    VITE_PROXY_REWRITE_TO: '',
  },
  staging: {
    VITE_PROXY_TARGET: 'https://erp-staging.gbuilderchina.com/apiv2/',
    VITE_PROXY_REWRITE_FROM: '^/api',
    VITE_PROXY_REWRITE_TO: '',
  },
  production: {
    VITE_PROXY_TARGET: 'https://erp.gbuilderchina.com/apiv2/',
    VITE_PROXY_REWRITE_FROM: '^/api',
    VITE_PROXY_REWRITE_TO: '',
  },
};

/**
 * 读取现有的 .env.local 文件
 */
function readEnvLocal() {
  const envPath = path.join(process.cwd(), '.env.local');
  if (!fs.existsSync(envPath)) {
    return {};
  }

  const content = fs.readFileSync(envPath, 'utf-8');
  const env = {};
  
  content.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });

  return env;
}

/**
 * 写入 .env.local 文件
 */
function writeEnvLocal(env) {
  const envPath = path.join(process.cwd(), '.env.local');
  const lines = [
    '# 本地环境配置（此文件不会被提交到 Git）',
    '# 由 switch-proxy.js 脚本自动生成',
    '',
  ];

  // 添加代理配置注释
  lines.push('# ================================');
  lines.push('# 代理配置');
  lines.push('# ================================');
  lines.push('');

  // 写入环境变量
  Object.entries(env).forEach(([key, value]) => {
    lines.push(`${key}=${value}`);
  });

  lines.push('');
  lines.push('# ================================');
  lines.push('# 其他配置');
  lines.push('# ================================');
  lines.push('');

  fs.writeFileSync(envPath, lines.join('\n'), 'utf-8');
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log('代理环境切换脚本');
  console.log('');
  console.log('用法：');
  console.log('  node scripts/switch-proxy.js [环境名称]');
  console.log('');
  console.log('可用环境：');
  Object.keys(ENV_PRESETS).forEach(env => {
    const config = ENV_PRESETS[env];
    console.log(`  ${env.padEnd(12)} - ${config.VITE_PROXY_TARGET}`);
  });
  console.log('');
  console.log('示例：');
  console.log('  node scripts/switch-proxy.js local    # 切换到本地环境');
  console.log('  node scripts/switch-proxy.js test     # 切换到测试环境');
  console.log('  node scripts/switch-proxy.js staging  # 切换到预发布环境');
  console.log('');
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    showHelp();
    return;
  }

  const envName = args[0];
  const preset = ENV_PRESETS[envName];

  if (!preset) {
    console.error(`❌ 未知的环境名称: ${envName}`);
    console.error('');
    showHelp();
    process.exit(1);
  }

  try {
    // 读取现有配置
    const currentEnv = readEnvLocal();
    
    // 合并新的代理配置
    const newEnv = {
      ...currentEnv,
      ...preset,
      VITE_PROXY_PATH_PREFIX: '/api',
      VITE_PROXY_CHANGE_ORIGIN: 'true',
      VITE_PROXY_WS: 'true',
    };

    // 写入配置文件
    writeEnvLocal(newEnv);

    console.log(`✅ 已切换到 ${envName} 环境`);
    console.log(`📍 代理目标: ${preset.VITE_PROXY_TARGET}`);
    console.log(`🔄 路径重写: ${preset.VITE_PROXY_REWRITE_FROM} → ${preset.VITE_PROXY_REWRITE_TO || '(无)'}`);
    console.log('');
    console.log('💡 请重启开发服务器以应用新配置');
    
  } catch (error) {
    console.error('❌ 切换环境失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
