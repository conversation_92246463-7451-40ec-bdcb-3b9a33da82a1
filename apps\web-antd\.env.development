# 端口号
VITE_PORT=3101

VITE_BASE=/

# 接口地址
VITE_GLOB_API_URL=/api

# 是否开启 Nitro Mock服务，true 为开启，false 为关闭
VITE_NITRO_MOCK=false

# vue-router 的模式
VITE_ROUTER_HISTORY=hash

# 是否打开 devtools，true 为打开，false 为关闭
VITE_DEVTOOLS=false

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true

# ================================
# 代理配置 - 本地开发环境
# ================================

# 代理目标地址 - 本地后端服务
VITE_PROXY_TARGET=http://erpv2.com/api/
# VITE_PROXY_TARGET=https://erp.gbuilderchina.com/testapiv2/
# 代理路径前缀
VITE_PROXY_PATH_PREFIX=/api

# 路径重写规则 - 本地环境需要将 /api 重写为 /apiv2
VITE_PROXY_REWRITE_FROM=^/api
VITE_PROXY_REWRITE_TO=

# 代理选项
VITE_PROXY_CHANGE_ORIGIN=true
VITE_PROXY_WS=true

# 其他 API 地址配置
VITE_GLOB_OTHER_API_URL=https://mock-napi.vben.pro/other-api
