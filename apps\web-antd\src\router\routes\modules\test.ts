import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:test-tube',
      order: 9999,
      title: $t('page.test.title'),
    },
    name: 'Test',
    path: '/test',
    children: [
      {
        name: 'UploadString',
        path: '/test/upload-string',
        component: () => import('#/views/test/upload-string.vue'),
        meta: {
          icon: 'lucide:upload',
          title: 'Upload 字符串测试',
        },
      },
      {
        name: 'UploadSimple',
        path: '/test/upload-simple',
        component: () => import('#/views/test/upload-simple.vue'),
        meta: {
          icon: 'lucide:upload-cloud',
          title: 'Upload 简化测试',
        },
      },
    ],
  },
];

export default routes;
