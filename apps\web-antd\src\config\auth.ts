/**
 * 认证相关配置
 */

/**
 * Token失效跳转配置
 */
export const AUTH_CONFIG = {
  /**
   * Token失效时跳转的页面URL
   * 可以是相对路径或绝对URL
   *
   * 示例：
   * - '/login' - 跳转到当前域名下的登录页面
   * - 'https://auth.example.com/login' - 跳转到其他域名的登录页面
   * - 'https://sso.example.com/login' - 跳转到SSO登录页面
   */
  AUTH_REDIRECT_URL: 'https://auth.georgebuilder.com/login',

  /**
   * 本地后端认证URL（用于获取token）
   */
  LOCAL_AUTH_URL: 'http://erpv2.com/api/',
} as const;

/**
 * 判断是否为本地后端环境
 * @returns 是否为本地后端
 */
function isLocalBackend(): boolean {
  const proxyTarget = import.meta.env.VITE_PROXY_TARGET;
  return (
    proxyTarget &&
    (proxyTarget.includes('erpv2.com') ||
      proxyTarget.includes('localhost') ||
      proxyTarget.includes('127.0.0.1') ||
      proxyTarget.includes('0.0.0.0'))
  );
}

/**
 * 构建Token失效跳转URL
 * @returns 跳转URL
 */
export function buildAuthRedirectUrl(): string {
  // 如果是本地后端环境，跳转到本地认证页面并添加 token 参数
  if (isLocalBackend()) {
    const currentUrl = window.location.origin + window.location.pathname;
    return `${AUTH_CONFIG.LOCAL_AUTH_URL}#/token=${encodeURIComponent(currentUrl)}`;
  }

  // 否则使用默认的认证跳转URL
  return AUTH_CONFIG.AUTH_REDIRECT_URL;
}
