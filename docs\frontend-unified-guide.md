# 前端统一使用指南

## 概述

本文档涵盖了三个核心转换方法的前端使用指南：

1. **transformBackendSearchToSchema** - 表单字段转换
2. **transformColumns** - 表格列转换
3. **transformToGroupedDescriptions** - 详情描述转换

## 1. 表单字段转换 (transformBackendSearchToSchema)

### 1.1 基础使用

```typescript
import { transformBackendSearchToSchema } from '#/utils/search-schema/transform';

// 后端返回的字段配置
const backendFields = [
  {
    field: 'name',
    title: '姓名',
    type: 'input',
    required: true,
    config: {
      placeholder: '请输入姓名',
    },
  },
  {
    field: 'total_price',
    title: '总价',
    type: 'number',
    config: {
      readonly: true,
      calculation: {
        type: 'product',
        sourceFields: ['unit_price', 'quantity'],
        triggerFields: ['unit_price', 'quantity'],
        precision: 2,
        defaultValue: 0,
      },
    },
  },
];

// 转换为前端表单配置
const formSchema = transformBackendSearchToSchema(backendFields);
```

### 1.2 在 Vue 组件中使用

```vue
<template>
  <div>
    <VbenForm ref="formRef" :schema="formSchema" @submit="handleSubmit" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { VbenForm } from '@vben/common-ui';
import { transformBackendSearchToSchema } from '#/utils/search-schema/transform';

const formRef = ref();
const formSchema = ref([]);

// 获取表单配置
const getFormConfig = async () => {
  try {
    const response = await fetch('/api/form-config');
    const data = await response.json();

    // 转换后端配置为前端表单配置
    formSchema.value = transformBackendSearchToSchema(data.fields);
  } catch (error) {
    console.error('获取表单配置失败:', error);
  }
};

// 表单提交
const handleSubmit = (values) => {
  console.log('表单数据:', values);
  // 处理表单提交逻辑
};

onMounted(() => {
  getFormConfig();
});
</script>
```

### 1.3 支持的转换特性

#### 1.3.1 字段类型映射

```typescript
// 后端配置
const backendField = {
  field: 'price',
  title: '价格',
  type: 'number',
  config: {
    precision: 2,
    min: 0,
    prefix: '¥',
  },
};

// 转换后的前端配置
const frontendSchema = {
  fieldName: 'price',
  component: 'InputNumber',
  label: '价格',
  componentProps: {
    precision: 2,
    min: 0,
    addonBefore: '¥',
  },
};
```

#### 1.3.2 联动计算配置

```typescript
// 方案一：config.calculation
const calculationField = {
  field: 'total_price',
  title: '总价',
  type: 'number',
  config: {
    readonly: true,
    calculation: {
      type: 'product',
      sourceFields: ['unit_price', 'quantity'],
      triggerFields: ['unit_price', 'quantity'],
      precision: 2,
      defaultValue: 0,
    },
  },
};

// 转换后自动生成联动依赖
const transformedSchema = {
  fieldName: 'total_price',
  component: 'InputNumber',
  label: '总价',
  componentProps: {
    readonly: true,
    precision: 2,
  },
  dependencies: {
    triggerFields: ['unit_price', 'quantity'],
    trigger: (values, formApi) => {
      const result = (values.unit_price || 0) * (values.quantity || 0);
      formApi.setFieldValue('total_price', Number(result.toFixed(2)));
    },
  },
};
```

#### 1.3.3 表格数据计算配置

```typescript
// 后端表格数据计算配置
const tableCalculationField = {
  field: 'order_total',
  title: '订单总金额',
  type: 'number',
  config: {
    readonly: true,
    prefix: '¥',
    calculation: {
      type: 'sum',
      tableFields: ['amount'], // 表格中的金额列
      triggerFields: ['order_items'], // 表格数据字段
      precision: 2,
      defaultValue: 0,
    },
  },
};

// 转换后的前端配置
const transformedTableCalculationSchema = {
  fieldName: 'order_total',
  component: 'InputNumber',
  label: '订单总金额',
  componentProps: {
    readonly: true,
    addonBefore: '¥',
    precision: 2,
  },
  dependencies: {
    triggerFields: ['order_items'],
    trigger: (values, formApi) => {
      const orderItems = values.order_items || [];
      const total = orderItems.reduce((sum, item) => {
        return sum + (Number(item.amount) || 0);
      }, 0);
      formApi.setFieldValue('order_total', Number(total.toFixed(2)));
    },
  },
};
```

#### 1.3.4 复杂表格计算配置

```typescript
// 后端复杂表格计算配置
const complexTableCalculationFields = [
  // 商品总数量
  {
    field: 'total_quantity',
    title: '商品总数量',
    type: 'number',
    config: {
      readonly: true,
      suffix: '件',
      calculation: {
        type: 'sum',
        tableFields: ['quantity'],
        triggerFields: ['order_items'],
        precision: 0,
        defaultValue: 0,
      },
    },
  },
  // 平均单价
  {
    field: 'average_price',
    title: '平均单价',
    type: 'number',
    config: {
      readonly: true,
      prefix: '¥',
      calculation: {
        type: 'average',
        tableFields: ['unit_price'],
        triggerFields: ['order_items'],
        precision: 2,
        defaultValue: 0,
      },
    },
  },
  // 最高单价
  {
    field: 'max_price',
    title: '最高单价',
    type: 'number',
    config: {
      readonly: true,
      prefix: '¥',
      calculation: {
        type: 'max',
        tableFields: ['unit_price'],
        triggerFields: ['order_items'],
        precision: 2,
        defaultValue: 0,
      },
    },
  },
  // 条件计算 - 电子产品总金额
  {
    field: 'electronics_total',
    title: '电子产品总金额',
    type: 'number',
    config: {
      readonly: true,
      prefix: '¥',
      calculation: {
        type: 'sum',
        tableFields: ['amount'],
        triggerFields: ['order_items'],
        filter: [
          {
            field: 'category',
            operator: '=',
            value: 'electronics',
          },
        ],
        precision: 2,
        defaultValue: 0,
      },
    },
  },
];

// 转换后的前端配置
const transformedComplexTableSchemas = [
  {
    fieldName: 'total_quantity',
    component: 'InputNumber',
    label: '商品总数量',
    componentProps: {
      readonly: true,
      addonAfter: '件',
      precision: 0,
    },
    dependencies: {
      triggerFields: ['order_items'],
      trigger: (values, formApi) => {
        const orderItems = values.order_items || [];
        const total = orderItems.reduce((sum, item) => {
          return sum + (Number(item.quantity) || 0);
        }, 0);
        formApi.setFieldValue('total_quantity', total);
      },
    },
  },
  {
    fieldName: 'average_price',
    component: 'InputNumber',
    label: '平均单价',
    componentProps: {
      readonly: true,
      addonBefore: '¥',
      precision: 2,
    },
    dependencies: {
      triggerFields: ['order_items'],
      trigger: (values, formApi) => {
        const orderItems = values.order_items || [];
        if (orderItems.length === 0) {
          formApi.setFieldValue('average_price', 0);
          return;
        }
        const total = orderItems.reduce((sum, item) => {
          return sum + (Number(item.unit_price) || 0);
        }, 0);
        const average = total / orderItems.length;
        formApi.setFieldValue('average_price', Number(average.toFixed(2)));
      },
    },
  },
  {
    fieldName: 'max_price',
    component: 'InputNumber',
    label: '最高单价',
    componentProps: {
      readonly: true,
      addonBefore: '¥',
      precision: 2,
    },
    dependencies: {
      triggerFields: ['order_items'],
      trigger: (values, formApi) => {
        const orderItems = values.order_items || [];
        if (orderItems.length === 0) {
          formApi.setFieldValue('max_price', 0);
          return;
        }
        const prices = orderItems.map((item) => Number(item.unit_price) || 0);
        const maxPrice = Math.max(...prices);
        formApi.setFieldValue('max_price', Number(maxPrice.toFixed(2)));
      },
    },
  },
  {
    fieldName: 'electronics_total',
    component: 'InputNumber',
    label: '电子产品总金额',
    componentProps: {
      readonly: true,
      addonBefore: '¥',
      precision: 2,
    },
    dependencies: {
      triggerFields: ['order_items'],
      trigger: (values, formApi) => {
        const orderItems = values.order_items || [];
        const electronicsTotal = orderItems
          .filter((item) => item.category === 'electronics')
          .reduce((sum, item) => {
            return sum + (Number(item.amount) || 0);
          }, 0);
        formApi.setFieldValue(
          'electronics_total',
          Number(electronicsTotal.toFixed(2)),
        );
      },
    },
  },
];
```

#### 1.3.5 混合计算配置（表单字段 + 表格数据）

```typescript
// 后端混合计算配置
const mixedCalculationField = {
  field: 'total_with_tax',
  title: '含税总价',
  type: 'number',
  config: {
    readonly: true,
    prefix: '¥',
    calculation: {
      type: 'formula',
      formula: 'order_total + tax_amount',
      sourceFields: ['tax_amount'], // 表单字段
      tableFields: ['amount'], // 表格字段
      triggerFields: ['order_items', 'tax_amount'],
      precision: 2,
      defaultValue: 0,
    },
  },
};

// 转换后的前端配置
const transformedMixedCalculationSchema = {
  fieldName: 'total_with_tax',
  component: 'InputNumber',
  label: '含税总价',
  componentProps: {
    readonly: true,
    addonBefore: '¥',
    precision: 2,
  },
  dependencies: {
    triggerFields: ['order_items', 'tax_amount'],
    trigger: (values, formApi) => {
      // 计算订单总金额（表格数据）
      const orderItems = values.order_items || [];
      const orderTotal = orderItems.reduce((sum, item) => {
        return sum + (Number(item.amount) || 0);
      }, 0);

      // 获取税费（表单字段）
      const taxAmount = Number(values.tax_amount) || 0;

      // 计算含税总价
      const totalWithTax = orderTotal + taxAmount;
      formApi.setFieldValue('total_with_tax', Number(totalWithTax.toFixed(2)));
    },
  },
};
```

#### 1.3.7 联动显示配置

```typescript
// 后端联动显示配置
const linkageField = {
  field: 'city',
  title: '城市',
  type: 'select',
  linkage: {
    triggerFields: ['province'],
    rules: {
      visibility: {
        showWhen: [
          {
            field: 'province',
            operator: '=',
            value: 'guangdong',
          },
        ],
      },
    },
  },
};

// 转换后的前端配置
const transformedLinkageSchema = {
  fieldName: 'city',
  component: 'Select',
  label: '城市',
  dependencies: {
    triggerFields: ['province'],
    show: (values) => {
      return values.province === 'guangdong';
    },
  },
};
```

#### 1.3.8 联动赋值配置

```typescript
// 后端联动赋值配置
const assignmentField = {
  field: 'department_id',
  title: '部门',
  type: 'apiSelect',
  config: {
    url: '/api/departments',
    linkageAssignment: {
      targetFields: [
        {
          field: 'department_name',
          valueMapping: 'label',
          clearOnEmpty: true,
        },
        {
          field: 'manager_id',
          valueMapping: 'manager_id',
          clearOnEmpty: true,
        },
      ],
    },
  },
};

// 转换后的前端配置
const transformedAssignmentSchema = {
  fieldName: 'department_id',
  component: 'ApiSelect',
  label: '部门',
  componentProps: {
    api: async (params) => {
      const response = await fetch('/api/departments', {
        method: 'POST',
        body: JSON.stringify(params),
      });
      return response.json();
    },
  },
  dependencies: {
    triggerFields: ['department_id'],
    trigger: (values, formApi) => {
      const selectedOption = formApi.getSelectedOption?.('department_id');
      if (selectedOption) {
        formApi.setFieldValue('department_name', selectedOption.label);
        formApi.setFieldValue('manager_id', selectedOption.manager_id);
      } else {
        formApi.setFieldValue('department_name', '');
        formApi.setFieldValue('manager_id', '');
      }
    },
  },
};
```

#### 1.3.9 API 组件自动配置

```typescript
// 后端 API 组件配置
const apiSelectField = {
  field: 'user_id',
  title: '用户',
  type: 'apiSelect',
  config: {
    url: '/api/users/search',
    labelField: 'name',
    valueField: 'id',
    showSearch: true,
    searchKey: 'keyword',
  },
};

// 转换后自动生成 API 函数
const transformedApiSchema = {
  fieldName: 'user_id',
  component: 'ApiSelect',
  label: '用户',
  componentProps: {
    api: async (params) => {
      const response = await fetch('/api/users/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params),
      });
      const data = await response.json();
      return data.items || data;
    },
    labelField: 'name',
    valueField: 'id',
    showSearch: true,
    searchKey: 'keyword',
  },
};
```

## 2. 表格列转换 (transformColumns)

### 2.1 基础使用

```typescript
import { transformColumns } from '#/utils/search-schema/transform';

// 后端返回的列配置
const backendColumns = [
  {
    field: 'name',
    title: '姓名',
    type: 'text',
    width: 120,
  },
  {
    field: 'status',
    title: '状态',
    type: 'switch',
    width: 80,
    config: {
      checkedValue: 1,
      uncheckedValue: 0,
    },
  },
  {
    field: 'actions',
    title: '操作',
    type: 'actions',
    width: 150,
    fixed: 'right',
    config: {
      buttons: [
        { title: '编辑', type: 'edit', key: 'edit' },
        { title: '删除', type: 'delete', key: 'delete' },
      ],
    },
  },
];

// 转换为前端表格列配置
const tableColumns = transformColumns(backendColumns);
```

### 2.2 在 Vue 组件中使用

```vue
<template>
  <div>
    <VbenTable
      :columns="tableColumns"
      :data="tableData"
      @action-click="handleActionClick"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { VbenTable } from '@vben/common-ui';
import { transformColumns } from '#/utils/search-schema/transform';

const tableColumns = ref([]);
const tableData = ref([]);

// 获取表格配置
const getTableConfig = async () => {
  try {
    const response = await fetch('/api/table-config');
    const data = await response.json();

    // 转换后端配置为前端表格配置
    tableColumns.value = transformColumns(data.columns);
  } catch (error) {
    console.error('获取表格配置失败:', error);
  }
};

// 获取表格数据
const getTableData = async () => {
  try {
    const response = await fetch('/api/table-data');
    const data = await response.json();
    tableData.value = data.items;
  } catch (error) {
    console.error('获取表格数据失败:', error);
  }
};

// 处理操作按钮点击
const handleActionClick = (action, row) => {
  console.log('操作:', action, '行数据:', row);

  switch (action.key) {
    case 'edit':
      // 处理编辑逻辑
      break;
    case 'delete':
      // 处理删除逻辑
      break;
    default:
      console.log('未知操作:', action);
  }
};

onMounted(() => {
  getTableConfig();
  getTableData();
});
</script>
```

### 2.3 支持的转换特性

#### 2.3.1 列类型映射

```typescript
// 后端列配置
const backendColumns = [
  {
    field: 'price',
    title: '价格',
    type: 'number',
    width: 120,
    config: {
      precision: 2,
      prefix: '¥',
      separator: ',',
    },
  },
  {
    field: 'status',
    title: '状态',
    type: 'switch',
    width: 80,
    config: {
      checkedValue: 1,
      uncheckedValue: 0,
      checkedText: '启用',
      uncheckedText: '禁用',
    },
  },
];

// 转换后的前端列配置
const transformedColumns = [
  {
    key: 'price',
    title: '价格',
    dataIndex: 'price',
    width: 120,
    customRender: ({ record }) => {
      const value = record.price || 0;
      return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
    },
  },
  {
    key: 'status',
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ record }) => {
      return h(Switch, {
        checked: record.status === 1,
        checkedChildren: '启用',
        unCheckedChildren: '禁用',
        disabled: true,
      });
    },
  },
];
```

#### 2.3.2 操作按钮转换

```typescript
// 后端操作按钮配置
const backendActionsColumn = {
  field: 'actions',
  title: '操作',
  type: 'actions',
  width: 150,
  fixed: 'right',
  config: {
    buttons: [
      {
        title: '编辑',
        type: 'edit',
        key: 'edit',
        show: {
          field: 'status',
          operator: '!=',
          value: 'locked',
        },
      },
      {
        title: '删除',
        type: 'delete',
        key: 'delete',
        confirm: '确定要删除这条记录吗？',
      },
    ],
  },
};

// 转换后的前端配置
const transformedActionsColumn = {
  key: 'actions',
  title: '操作',
  dataIndex: 'actions',
  width: 150,
  fixed: 'right',
  customRender: ({ record }) => {
    const buttons = [];

    // 编辑按钮 - 条件显示
    if (record.status !== 'locked') {
      buttons.push(
        h(
          Button,
          {
            type: 'link',
            size: 'small',
            onClick: () =>
              emit('action-click', { key: 'edit', type: 'edit' }, record),
          },
          '编辑',
        ),
      );
    }

    // 删除按钮 - 带确认
    buttons.push(
      h(
        Popconfirm,
        {
          title: '确定要删除这条记录吗？',
          onConfirm: () =>
            emit('action-click', { key: 'delete', type: 'delete' }, record),
        },
        {
          default: () =>
            h(
              Button,
              {
                type: 'link',
                size: 'small',
                danger: true,
              },
              '删除',
            ),
        },
      ),
    );

    return h('div', { class: 'action-buttons' }, buttons);
  },
};
```

#### 2.3.3 固定列和排序

```typescript
// 后端列配置
const backendColumn = {
  field: 'name',
  title: '姓名',
  type: 'text',
  width: 120,
  fixed: 'left',
  sortable: true,
};

// 转换后的前端配置
const transformedColumn = {
  key: 'name',
  title: '姓名',
  dataIndex: 'name',
  width: 120,
  fixed: 'left',
  sorter: true,
  sortDirections: ['ascend', 'descend'],
};
```

#### 2.3.4 格式化显示

```typescript
// 后端日期列配置
const backendDateColumn = {
  field: 'created_at',
  title: '创建时间',
  type: 'datetime',
  width: 160,
  config: {
    format: 'YYYY-MM-DD HH:mm:ss',
    emptyText: '-',
  },
};

// 转换后的前端配置
const transformedDateColumn = {
  key: 'created_at',
  title: '创建时间',
  dataIndex: 'created_at',
  width: 160,
  customRender: ({ record }) => {
    const value = record.created_at;
    if (!value) return '-';
    return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
  },
};
```

#### 2.3.5 图片列转换

```typescript
// 后端图片列配置
const backendImageColumn = {
  field: 'avatar',
  title: '头像',
  type: 'image',
  width: 80,
  config: {
    width: 60,
    height: 60,
    preview: true,
    fallback: '/default-avatar.png',
  },
};

// 转换后的前端配置
const transformedImageColumn = {
  key: 'avatar',
  title: '头像',
  dataIndex: 'avatar',
  width: 80,
  customRender: ({ record }) => {
    return h(Image, {
      src: record.avatar || '/default-avatar.png',
      width: 60,
      height: 60,
      style: { borderRadius: '4px' },
      preview: true,
      fallback: '/default-avatar.png',
    });
  },
};
```

## 3. 详情描述转换 (transformToGroupedDescriptions)

### 3.1 基础使用

```typescript
import { transformToGroupedDescriptions } from '#/utils/search-schema/transform';

// 后端返回的描述配置
const backendDescriptions = [
  {
    label: '基本信息',
    items: [
      {
        field: 'name',
        title: '姓名',
        type: 'text',
        span: 1,
      },
      {
        field: 'email',
        title: '邮箱',
        type: 'text',
        span: 1,
      },
    ],
  },
  {
    label: '状态信息',
    items: [
      {
        field: 'status',
        title: '状态',
        type: 'switch',
        span: 1,
        config: {
          checkedValue: 1,
          uncheckedValue: 0,
        },
      },
    ],
  },
];

// 转换为前端描述配置
const descriptions = transformToGroupedDescriptions(backendDescriptions);
```

### 3.2 在 Vue 组件中使用

```vue
<template>
  <div>
    <VbenDescriptions
      v-for="(group, index) in descriptions"
      :key="index"
      :title="group.label"
      :items="group.items"
      :data="detailData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { VbenDescriptions } from '@vben/common-ui';
import { transformToGroupedDescriptions } from '#/utils/search-schema/transform';

const descriptions = ref([]);
const detailData = ref({});

// 获取详情配置
const getDetailConfig = async () => {
  try {
    const response = await fetch('/api/detail-config');
    const data = await response.json();

    // 转换后端配置为前端描述配置
    descriptions.value = transformToGroupedDescriptions(data.descriptions);
  } catch (error) {
    console.error('获取详情配置失败:', error);
  }
};

// 获取详情数据
const getDetailData = async (id) => {
  try {
    const response = await fetch(`/api/detail/${id}`);
    const data = await response.json();
    detailData.value = data;
  } catch (error) {
    console.error('获取详情数据失败:', error);
  }
};

onMounted(() => {
  getDetailConfig();
  // 假设从路由参数获取 ID
  const id = route.params.id;
  if (id) {
    getDetailData(id);
  }
});
</script>
```

### 3.3 支持的转换特性

#### 3.3.1 分组显示转换

```typescript
// 后端分组配置
const backendDescriptions = [
  {
    label: '基本信息',
    items: [
      {
        field: 'name',
        title: '姓名',
        type: 'text',
        span: 1,
      },
      {
        field: 'age',
        title: '年龄',
        type: 'number',
        span: 1,
        config: {
          suffix: '岁',
        },
      },
    ],
  },
  {
    label: '联系信息',
    items: [
      {
        field: 'email',
        title: '邮箱',
        type: 'text',
        span: 2,
        config: {
          copyable: true,
        },
      },
    ],
  },
];

// 转换后的前端配置
const transformedDescriptions = [
  {
    label: '基本信息',
    items: [
      {
        key: 'name',
        label: '姓名',
        span: 1,
        render: (value) => value || '-',
      },
      {
        key: 'age',
        label: '年龄',
        span: 1,
        render: (value) => (value ? `${value}岁` : '-'),
      },
    ],
  },
  {
    label: '联系信息',
    items: [
      {
        key: 'email',
        label: '邮箱',
        span: 2,
        render: (value) => {
          if (!value) return '-';
          return h('div', [
            h('span', value),
            h(Button, {
              type: 'link',
              size: 'small',
              icon: h(CopyOutlined),
              onClick: () => navigator.clipboard.writeText(value),
            }),
          ]);
        },
      },
    ],
  },
];
```

#### 3.3.2 布局控制转换

```typescript
// 后端布局配置
const backendLayoutDescriptions = [
  {
    label: '详细信息',
    items: [
      {
        field: 'title',
        title: '标题',
        type: 'text',
        span: 4, // 占满整行
      },
      {
        field: 'category',
        title: '分类',
        type: 'text',
        span: 1, // 占1/4
      },
      {
        field: 'status',
        title: '状态',
        type: 'switch',
        span: 1, // 占1/4
      },
      {
        field: 'price',
        title: '价格',
        type: 'number',
        span: 2, // 占1/2
        config: {
          precision: 2,
          prefix: '¥',
        },
      },
    ],
  },
];

// 转换后的前端配置（响应式布局）
const transformedLayoutDescriptions = [
  {
    label: '详细信息',
    column: { xs: 1, sm: 2, md: 4 }, // 响应式列数
    items: [
      {
        key: 'title',
        label: '标题',
        span: { xs: 1, sm: 2, md: 4 }, // 响应式span
        render: (value) => value || '-',
      },
      {
        key: 'category',
        label: '分类',
        span: { xs: 1, sm: 1, md: 1 },
        render: (value) => value || '-',
      },
      {
        key: 'status',
        label: '状态',
        span: { xs: 1, sm: 1, md: 1 },
        render: (value) => {
          return h(Switch, {
            checked: value === 1,
            disabled: true,
          });
        },
      },
      {
        key: 'price',
        label: '价格',
        span: { xs: 1, sm: 2, md: 2 },
        render: (value) => {
          if (!value) return '-';
          return `¥${Number(value).toFixed(2)}`;
        },
      },
    ],
  },
];
```

#### 3.3.3 类型转换示例

```typescript
// 后端多种类型配置
const backendTypeDescriptions = [
  {
    field: 'avatar',
    title: '头像',
    type: 'image',
    span: 1,
    config: {
      width: 80,
      height: 80,
      preview: true,
    },
  },
  {
    field: 'tags',
    title: '标签',
    type: 'tag',
    span: 2,
    config: {
      multiple: true,
      separator: ',',
      color: 'blue',
    },
  },
  {
    field: 'website',
    title: '网站',
    type: 'link',
    span: 1,
    config: {
      target: '_blank',
    },
  },
];

// 转换后的前端渲染器
const transformedTypeDescriptions = [
  {
    key: 'avatar',
    label: '头像',
    span: 1,
    render: (value) => {
      if (!value) return '-';
      return h(Image, {
        src: value,
        width: 80,
        height: 80,
        preview: true,
        style: { borderRadius: '4px' },
      });
    },
  },
  {
    key: 'tags',
    label: '标签',
    span: 2,
    render: (value) => {
      if (!value) return '-';
      const tags = value.split(',');
      return h(
        'div',
        tags.map((tag) => h(Tag, { color: 'blue', key: tag }, tag.trim())),
      );
    },
  },
  {
    key: 'website',
    label: '网站',
    span: 1,
    render: (value) => {
      if (!value) return '-';
      return h(
        'a',
        {
          href: value,
          target: '_blank',
          rel: 'noopener noreferrer',
        },
        value,
      );
    },
  },
];
```

#### 3.3.4 格式化显示转换

```typescript
// 后端格式化配置
const backendFormatDescriptions = [
  {
    field: 'created_at',
    title: '创建时间',
    type: 'datetime',
    span: 2,
    config: {
      format: 'YYYY-MM-DD HH:mm:ss',
      relative: true, // 显示相对时间
    },
  },
  {
    field: 'file_size',
    title: '文件大小',
    type: 'number',
    span: 1,
    config: {
      formatter: 'fileSize', // 文件大小格式化
    },
  },
  {
    field: 'progress',
    title: '进度',
    type: 'progress',
    span: 1,
    config: {
      showPercent: true,
      status: 'active',
    },
  },
];

// 转换后的前端格式化渲染
const transformedFormatDescriptions = [
  {
    key: 'created_at',
    label: '创建时间',
    span: 2,
    render: (value) => {
      if (!value) return '-';
      const date = dayjs(value);
      return h('div', [
        h('div', date.format('YYYY-MM-DD HH:mm:ss')),
        h(
          'div',
          {
            style: { fontSize: '12px', color: '#999' },
          },
          date.fromNow(),
        ),
      ]);
    },
  },
  {
    key: 'file_size',
    label: '文件大小',
    span: 1,
    render: (value) => {
      if (!value) return '-';
      const sizes = ['B', 'KB', 'MB', 'GB'];
      let size = value;
      let unit = 0;
      while (size >= 1024 && unit < sizes.length - 1) {
        size /= 1024;
        unit++;
      }
      return `${size.toFixed(2)} ${sizes[unit]}`;
    },
  },
  {
    key: 'progress',
    label: '进度',
    span: 1,
    render: (value) => {
      if (value === null || value === undefined) return '-';
      return h(Progress, {
        percent: value,
        status: value === 100 ? 'success' : 'active',
        showInfo: true,
      });
    },
  },
];
```

## 4. 完整示例

### 4.1 用户管理页面

```vue
<template>
  <div class="user-management">
    <!-- 搜索表单 -->
    <div class="search-form">
      <VbenForm
        ref="searchFormRef"
        :schema="searchSchema"
        @submit="handleSearch"
        @reset="handleReset"
      />
    </div>

    <!-- 数据表格 -->
    <div class="data-table">
      <VbenTable
        :columns="tableColumns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        @action-click="handleActionClick"
        @page-change="handlePageChange"
      />
    </div>

    <!-- 详情抽屉 -->
    <VbenDrawer v-model:open="detailVisible" title="用户详情" width="600px">
      <div v-for="(group, index) in descriptions" :key="index">
        <VbenDescriptions
          :title="group.label"
          :items="group.items"
          :data="currentUser"
        />
      </div>
    </VbenDrawer>

    <!-- 编辑表单弹窗 -->
    <VbenModal
      v-model:open="editVisible"
      :title="isEdit ? '编辑用户' : '新增用户'"
      width="800px"
      @ok="handleSave"
    >
      <VbenForm ref="editFormRef" :schema="editSchema" />
    </VbenModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import {
  VbenForm,
  VbenTable,
  VbenDrawer,
  VbenModal,
  VbenDescriptions,
} from '@vben/common-ui';
import {
  transformBackendSearchToSchema,
  transformColumns,
  transformToGroupedDescriptions,
} from '#/utils/search-schema/transform';

// 响应式数据
const searchFormRef = ref();
const editFormRef = ref();
const searchSchema = ref([]);
const editSchema = ref([]);
const tableColumns = ref([]);
const descriptions = ref([]);
const tableData = ref([]);
const currentUser = ref({});
const loading = ref(false);
const detailVisible = ref(false);
const editVisible = ref(false);
const isEdit = ref(false);

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 获取配置
const getConfigs = async () => {
  try {
    const [searchRes, tableRes, detailRes] = await Promise.all([
      fetch('/api/user/search-config'),
      fetch('/api/user/table-config'),
      fetch('/api/user/detail-config'),
    ]);

    const [searchData, tableData, detailData] = await Promise.all([
      searchRes.json(),
      tableRes.json(),
      detailRes.json(),
    ]);

    // 转换配置
    searchSchema.value = transformBackendSearchToSchema(searchData.fields);
    editSchema.value = transformBackendSearchToSchema(searchData.fields); // 编辑表单复用搜索配置
    tableColumns.value = transformColumns(tableData.columns);
    descriptions.value = transformToGroupedDescriptions(
      detailData.descriptions,
    );
  } catch (error) {
    console.error('获取配置失败:', error);
  }
};

// 获取表格数据
const getTableData = async (params = {}) => {
  loading.value = true;
  try {
    const response = await fetch('/api/users', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...params,
        page: pagination.current,
        pageSize: pagination.pageSize,
      }),
    });

    const data = await response.json();
    tableData.value = data.items;
    pagination.total = data.total;
  } catch (error) {
    console.error('获取数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = (values) => {
  pagination.current = 1;
  getTableData(values);
};

// 重置处理
const handleReset = () => {
  pagination.current = 1;
  getTableData();
};

// 分页处理
const handlePageChange = (page, pageSize) => {
  pagination.current = page;
  pagination.pageSize = pageSize;
  getTableData(searchFormRef.value?.getFieldsValue());
};

// 操作按钮处理
const handleActionClick = (action, row) => {
  switch (action.key) {
    case 'view':
      currentUser.value = row;
      detailVisible.value = true;
      break;
    case 'edit':
      isEdit.value = true;
      editFormRef.value?.setFieldsValue(row);
      editVisible.value = true;
      break;
    case 'delete':
      handleDelete(row);
      break;
    default:
      console.log('未知操作:', action);
  }
};

// 删除处理
const handleDelete = async (row) => {
  try {
    await fetch(`/api/users/${row.id}`, { method: 'DELETE' });
    getTableData(searchFormRef.value?.getFieldsValue());
  } catch (error) {
    console.error('删除失败:', error);
  }
};

// 保存处理
const handleSave = async () => {
  try {
    const values = await editFormRef.value?.validate();
    const url = isEdit.value ? `/api/users/${values.id}` : '/api/users';
    const method = isEdit.value ? 'PUT' : 'POST';

    await fetch(url, {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(values),
    });

    editVisible.value = false;
    getTableData(searchFormRef.value?.getFieldsValue());
  } catch (error) {
    console.error('保存失败:', error);
  }
};

onMounted(() => {
  getConfigs();
  getTableData();
});
</script>

<style scoped>
.user-management {
  padding: 16px;
}

.search-form {
  background: white;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 6px;
}

.data-table {
  background: white;
  padding: 16px;
  border-radius: 6px;
}
</style>
```

## 5. 高级用法

### 5.1 自定义转换器

```typescript
// 自定义字段转换器
const customFieldTransformer = (field) => {
  if (field.type === 'custom-type') {
    return {
      fieldName: field.field,
      component: 'CustomComponent',
      componentProps: field.config,
    };
  }
  return null;
};

// 使用自定义转换器
const schema = transformBackendSearchToSchema(fields, {
  customTransformers: [customFieldTransformer],
});
```

### 5.2 动态配置更新

```typescript
// 监听配置变化
const updateConfig = async () => {
  const newConfig = await fetchConfig();

  // 动态更新表单配置
  searchSchema.value = transformBackendSearchToSchema(newConfig.fields);

  // 动态更新表格配置
  tableColumns.value = transformColumns(newConfig.columns);
};

// 响应式更新
watch(() => configVersion.value, updateConfig);
```

### 5.3 错误处理

```typescript
const safeTransform = (data, transformer) => {
  try {
    return transformer(data);
  } catch (error) {
    console.error('转换失败:', error);
    return [];
  }
};

// 安全转换
const schema = safeTransform(backendFields, transformBackendSearchToSchema);
const columns = safeTransform(backendColumns, transformColumns);
const descriptions = safeTransform(
  backendDescriptions,
  transformToGroupedDescriptions,
);
```

## 6. 最佳实践

### 6.1 性能优化

- **缓存配置**：将转换后的配置缓存，避免重复转换
- **按需加载**：根据页面需要动态加载配置
- **防抖处理**：对频繁的配置更新进行防抖处理

### 6.2 错误处理

- **容错机制**：为转换过程添加容错处理
- **日志记录**：记录转换过程中的错误和警告
- **降级方案**：提供配置转换失败时的降级方案

### 6.3 类型安全

- **TypeScript**：使用 TypeScript 确保类型安全
- **接口定义**：为后端配置定义清晰的接口类型
- **运行时验证**：添加运行时类型验证

### 6.4 可维护性

- **配置分离**：将配置逻辑与业务逻辑分离
- **文档完善**：为自定义配置添加详细文档
- **测试覆盖**：为转换函数编写完整的测试用例

## 总结

前端统一转换系统提供了：

✅ **自动转换**：后端配置自动转换为前端组件配置✅ **类型安全**：完整的 TypeScript 类型支持 ✅ **功能完整**：支持表单、表格、详情的完整功能✅ **易于使用**：简单的 API 调用即可完成转换 ✅ **高度可扩展**：支持自定义转换器和配置

通过这套转换系统，前端开发者可以专注于业务逻辑，而不需要关心复杂的配置转换过程。
