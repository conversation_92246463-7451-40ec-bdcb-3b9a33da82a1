<?php
/**
 * EditTable 外部联动功能示例
 * 
 * 这个示例展示了如何配置一个订单表单，其中订单明细表格的字段
 * 会根据外部的订单类型和客户等级动态显示/隐藏和设置必填状态
 */

// 订单表单配置
function getOrderFormConfig() {
    return [
        // 外部表单字段1：订单类型
        [
            'field' => 'order_type',
            'title' => '订单类型',
            'type' => 'select',
            'required' => true,
            'config' => [
                'placeholder' => '请选择订单类型',
                'options' => [
                    ['label' => '零售订单', 'value' => 'retail'],
                    ['label' => '批发订单', 'value' => 'wholesale'],
                    ['label' => '企业采购', 'value' => 'enterprise']
                ]
            ]
        ],
        
        // 外部表单字段2：客户等级
        [
            'field' => 'customer_level',
            'title' => '客户等级',
            'type' => 'select',
            'config' => [
                'placeholder' => '请选择客户等级',
                'options' => [
                    ['label' => '普通客户', 'value' => 'normal'],
                    ['label' => 'VIP客户', 'value' => 'vip'],
                    ['label' => '钻石客户', 'value' => 'diamond']
                ]
            ]
        ],
        
        // 外部表单字段3：销售区域
        [
            'field' => 'sales_region',
            'title' => '销售区域',
            'type' => 'select',
            'config' => [
                'placeholder' => '请选择销售区域',
                'options' => [
                    ['label' => '华北地区', 'value' => 'north'],
                    ['label' => '华南地区', 'value' => 'south'],
                    ['label' => '海外市场', 'value' => 'overseas']
                ]
            ]
        ],
        
        // EditTable 字段：订单明细（带外部联动）
        [
            'field' => 'order_items',
            'title' => '订单明细',
            'type' => 'edittable',
            'required' => true,
            'config' => [
                // 表格列配置
                'columns' => [
                    ['field' => 'product_name', 'title' => '商品名称', 'type' => 'text', 'width' => 150],
                    ['field' => 'quantity', 'title' => '数量', 'type' => 'number', 'width' => 80],
                    ['field' => 'retail_price', 'title' => '零售价', 'type' => 'number', 'width' => 100],
                    ['field' => 'wholesale_price', 'title' => '批发价', 'type' => 'number', 'width' => 100],
                    ['field' => 'vip_discount', 'title' => 'VIP折扣(%)', 'type' => 'number', 'width' => 120],
                    ['field' => 'tax_rate', 'title' => '税率(%)', 'type' => 'number', 'width' => 100],
                    ['field' => 'export_fee', 'title' => '出口费用', 'type' => 'number', 'width' => 100],
                    ['field' => 'remark', 'title' => '备注', 'type' => 'text', 'width' => 150]
                ],
                
                // 编辑表单字段配置
                'form' => [
                    [
                        'field' => 'product_name',
                        'title' => '商品名称',
                        'type' => 'input',
                        'required' => true,
                        'config' => [
                            'placeholder' => '请输入商品名称'
                        ]
                    ],
                    [
                        'field' => 'quantity',
                        'title' => '数量',
                        'type' => 'inputNumber',
                        'required' => true,
                        'config' => [
                            'min' => 1,
                            'placeholder' => '请输入数量'
                        ]
                    ],
                    [
                        'field' => 'retail_price',
                        'title' => '零售价',
                        'type' => 'inputNumber',
                        'config' => [
                            'min' => 0,
                            'precision' => 2,
                            'placeholder' => '请输入零售价'
                        ]
                    ],
                    [
                        'field' => 'wholesale_price',
                        'title' => '批发价',
                        'type' => 'inputNumber',
                        'config' => [
                            'min' => 0,
                            'precision' => 2,
                            'placeholder' => '请输入批发价'
                        ]
                    ],
                    [
                        'field' => 'vip_discount',
                        'title' => 'VIP折扣(%)',
                        'type' => 'inputNumber',
                        'config' => [
                            'min' => 0,
                            'max' => 100,
                            'precision' => 1,
                            'placeholder' => '请输入VIP折扣'
                        ]
                    ],
                    [
                        'field' => 'tax_rate',
                        'title' => '税率(%)',
                        'type' => 'inputNumber',
                        'config' => [
                            'min' => 0,
                            'max' => 100,
                            'precision' => 2,
                            'placeholder' => '请输入税率'
                        ]
                    ],
                    [
                        'field' => 'export_fee',
                        'title' => '出口费用',
                        'type' => 'inputNumber',
                        'config' => [
                            'min' => 0,
                            'precision' => 2,
                            'placeholder' => '请输入出口费用'
                        ]
                    ],
                    [
                        'field' => 'remark',
                        'title' => '备注',
                        'type' => 'textarea',
                        'config' => [
                            'placeholder' => '请输入备注信息',
                            'rows' => 3
                        ]
                    ]
                ],
                
                // 外部联动配置
                'externalLinkage' => [
                    // 监听的外部字段
                    'triggerFields' => ['order_type', 'customer_level', 'sales_region'],
                    
                    // 联动规则
                    'rules' => [
                        // 字段显示/隐藏规则
                        'visibility' => [
                            'targetFields' => [
                                // 零售订单显示零售价，隐藏批发价
                                [
                                    'field' => 'retail_price',
                                    'showWhen' => [
                                        [
                                            'field' => 'order_type',
                                            'operator' => 'in',
                                            'value' => ['retail', 'enterprise']
                                        ]
                                    ]
                                ],
                                [
                                    'field' => 'wholesale_price',
                                    'showWhen' => [
                                        [
                                            'field' => 'order_type',
                                            'operator' => '=',
                                            'value' => 'wholesale'
                                        ]
                                    ]
                                ],
                                
                                // VIP客户显示VIP折扣字段
                                [
                                    'field' => 'vip_discount',
                                    'showWhen' => [
                                        [
                                            'field' => 'customer_level',
                                            'operator' => 'in',
                                            'value' => ['vip', 'diamond']
                                        ]
                                    ]
                                ],
                                
                                // 海外销售显示出口费用字段
                                [
                                    'field' => 'export_fee',
                                    'showWhen' => [
                                        [
                                            'field' => 'sales_region',
                                            'operator' => '=',
                                            'value' => 'overseas'
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        
                        // 字段必填规则
                        'required' => [
                            'targetFields' => [
                                // 企业采购必须填写税率
                                [
                                    'field' => 'tax_rate',
                                    'requiredWhen' => [
                                        [
                                            'field' => 'order_type',
                                            'operator' => '=',
                                            'value' => 'enterprise'
                                        ]
                                    ]
                                ],
                                
                                // 海外销售必须填写出口费用
                                [
                                    'field' => 'export_fee',
                                    'requiredWhen' => [
                                        [
                                            'field' => 'sales_region',
                                            'operator' => '=',
                                            'value' => 'overseas'
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                
                // 默认数据
                'tabList' => [
                    [
                        'product_name' => '示例商品A',
                        'quantity' => 1,
                        'retail_price' => 100.00,
                        'wholesale_price' => 80.00,
                        'remark' => '示例数据'
                    ]
                ]
            ]
        ]
    ];
}

// 使用示例
$formConfig = getOrderFormConfig();

// 返回给前端
header('Content-Type: application/json');
echo json_encode([
    'code' => 200,
    'message' => 'success',
    'data' => $formConfig
]);
?>
