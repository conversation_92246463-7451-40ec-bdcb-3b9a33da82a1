# Token Refresh 功能恢复说明

## 📝 更改概述

已成功移除跳过 token refresh 接口的相关代码和配置，现在系统将正常调用 token refresh 接口。

## 🔧 具体更改

### 1. **环境变量配置**

**删除的环境变量**：
- `VITE_SKIP_TOKEN_REFRESH` - 已从所有环境配置文件中移除

**影响的文件**：
- `apps/web-antd/.env.development`
- `apps/web-antd/.env.local`
- `apps/web-antd/.env.local.backend`

### 2. **API 代码更改**

**文件**：`apps/web-antd/src/api/core/auth.ts`

**更改前**：
```javascript
function shouldSkipTokenRefresh(): boolean {
  // 检查环境变量和本地地址判断
  const skipTokenRefresh = import.meta.env.VITE_SKIP_TOKEN_REFRESH;
  if (skipTokenRefresh === 'true') {
    return true;
  }
  // ... 其他逻辑
}

export async function refreshToken() {
  if (shouldSkipTokenRefresh()) {
    console.warn('[Auth] 跳过 token refresh 接口');
    return { data: 'skipped', status: 200 };
  }
  return requestClient.get('/user/token/refresh');
}
```

**更改后**：
```javascript
export async function refreshToken() {
  return requestClient.get('/user/token/refresh');
}
```

### 3. **脚本文件更改**

**文件**：`apps/web-antd/switch-backend.js`
- 移除了 `VITE_SKIP_TOKEN_REFRESH=false` 配置

### 4. **文档更新**

**文件**：`apps/web-antd/BACKEND_SWITCH.md`
- 删除了关于跳过 token refresh 接口的说明
- 更新了相关配置示例

## 🚀 功能恢复

### ✅ **现在的行为**

1. **Token 过期时**：
   - 系统会正常调用 `/user/token/refresh` 接口
   - 如果刷新成功，继续原请求
   - 如果刷新失败，跳转到登录页面

2. **不再跳过**：
   - 无论是本地后端还是远程后端
   - 都会正常执行 token refresh 流程

3. **API 请求流程**：
   ```
   API 请求 → 401 错误 → 调用 refresh token → 重试原请求
   ```

### ⚠️ **注意事项**

1. **后端要求**：
   - 后端必须实现 `/user/token/refresh` 接口
   - 接口应该能够正确处理 token 刷新逻辑

2. **错误处理**：
   - 如果后端没有实现该接口，会出现 404 错误
   - 系统会自动跳转到登录页面

3. **开发环境**：
   - 本地开发时需要确保后端服务正常运行
   - 或者临时禁用 refresh token 功能（在 preferences.ts 中设置）

## 🔍 验证方法

1. **检查环境变量**：
   ```bash
   # 确认这些文件中不再包含 VITE_SKIP_TOKEN_REFRESH
   cat .env.development
   cat .env.local
   cat .env.local.backend
   ```

2. **检查 API 调用**：
   - 在浏览器开发者工具中观察网络请求
   - 当 token 过期时，应该能看到 `/user/token/refresh` 请求

3. **测试流程**：
   - 等待 token 过期或手动清除 token
   - 发起需要认证的 API 请求
   - 观察是否正常调用 refresh token 接口

## 📋 回滚方法

如果需要临时恢复跳过功能，可以：

1. **临时禁用 refresh token**（推荐）：
   ```typescript
   // 在 src/preferences.ts 中
   export const overridesPreferences = defineOverridesPreferences({
     app: {
       enableRefreshToken: false, // 设置为 false
     },
   });
   ```

2. **或者重新添加环境变量**：
   ```env
   # 在 .env.development 中添加
   VITE_SKIP_TOKEN_REFRESH=true
   ```
   然后恢复相关的跳过逻辑代码。

## ✅ **完成状态**

- ✅ 移除了所有跳过 token refresh 的代码
- ✅ 清理了相关环境变量配置
- ✅ 更新了文档说明
- ✅ 系统现在会正常调用 token refresh 接口
- ✅ 开发服务器已重启并应用更改
