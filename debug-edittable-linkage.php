<?php
/**
 * EditTable 外部联动调试配置
 * 用于排查外部联动不生效的问题
 */

// 完整的表单配置示例
function getDebugFormConfig() {
    return [
        // 外部表单字段：币种选择
        [
            'field' => 'currency',
            'title' => '币种',
            'type' => 'select',
            'required' => true,
            'config' => [
                'placeholder' => '请选择币种',
                'options' => [
                    ['label' => '人民币', 'value' => '人民币'],
                    ['label' => '美元', 'value' => '美元'],
                    ['label' => '欧元', 'value' => '欧元'],
                    ['label' => '日元', 'value' => '日元']
                ]
            ]
        ],
        
        // 其他外部字段（如果有的话）
        [
            'field' => 'order_type',
            'title' => '订单类型',
            'type' => 'select',
            'config' => [
                'options' => [
                    ['label' => '采购订单', 'value' => 'purchase'],
                    ['label' => '销售订单', 'value' => 'sales']
                ]
            ]
        ],
        
        // EditTable 字段（带外部联动）
        [
            'field' => 'order_items',
            'title' => '订单明细',
            'type' => 'edittable',
            'required' => true,
            'config' => [
                // 表格列配置
                'columns' => [
                    ['field' => 'product_name', 'title' => '商品名称', 'type' => 'text', 'width' => 150],
                    ['field' => 'quantity', 'title' => '数量', 'type' => 'number', 'width' => 80],
                    ['field' => 'unit_pirce', 'title' => '人民币单价', 'type' => 'number', 'width' => 120],
                    ['field' => 'foreign_currency_unit_pirce', 'title' => '外币单价', 'type' => 'number', 'width' => 120],
                    ['field' => 'total_amount', 'title' => '总金额', 'type' => 'number', 'width' => 120]
                ],
                
                // 编辑表单字段配置
                'form' => [
                    [
                        'field' => 'product_name',
                        'title' => '商品名称',
                        'type' => 'input',
                        'required' => true,
                        'config' => [
                            'placeholder' => '请输入商品名称'
                        ]
                    ],
                    [
                        'field' => 'quantity',
                        'title' => '数量',
                        'type' => 'inputNumber',
                        'required' => true,
                        'config' => [
                            'min' => 1,
                            'placeholder' => '请输入数量'
                        ]
                    ],
                    [
                        'field' => 'unit_pirce',
                        'title' => '人民币单价',
                        'type' => 'inputNumber',
                        'config' => [
                            'min' => 0,
                            'precision' => 2,
                            'placeholder' => '请输入人民币单价',
                            'addonAfter' => '元'
                        ]
                    ],
                    [
                        'field' => 'foreign_currency_unit_pirce',
                        'title' => '外币单价',
                        'type' => 'inputNumber',
                        'config' => [
                            'min' => 0,
                            'precision' => 2,
                            'placeholder' => '请输入外币单价'
                        ]
                    ],
                    [
                        'field' => 'total_amount',
                        'title' => '总金额',
                        'type' => 'inputNumber',
                        'config' => [
                            'readonly' => true,
                            'precision' => 2,
                            'placeholder' => '自动计算'
                        ]
                    ]
                ],
                
                // 外部联动配置
                'externalLinkage' => [
                    // 监听的外部表单字段
                    'triggerFields' => ['currency'],
                    
                    // 联动规则
                    'rules' => [
                        // 字段显示/隐藏规则
                        'visibility' => [
                            'targetFields' => [
                                // 非人民币时显示外币单价字段
                                [
                                    'field' => 'foreign_currency_unit_pirce',
                                    'showWhen' => [
                                        [
                                            'field' => 'currency',
                                            'operator' => '!=',
                                            'value' => '人民币'
                                        ]
                                    ]
                                ],
                                // 人民币时显示人民币单价字段
                                [
                                    'field' => 'unit_pirce',
                                    'showWhen' => [
                                        [
                                            'field' => 'currency',
                                            'operator' => '=',
                                            'value' => '人民币'
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                
                // 默认数据
                'tabList' => [
                    [
                        'product_name' => '示例商品',
                        'quantity' => 1,
                        'unit_pirce' => 100.00,
                        'foreign_currency_unit_pirce' => 15.00,
                        'total_amount' => 100.00
                    ]
                ]
            ]
        ]
    ];
}

// 调试信息输出
function debugLinkageConfig() {
    $config = getDebugFormConfig();
    
    echo "=== 调试信息 ===\n";
    echo "1. 外部触发字段: currency\n";
    echo "2. 目标字段: unit_pirce, foreign_currency_unit_pirce\n";
    echo "3. 联动规则: 根据币种显示对应的单价字段\n\n";
    
    echo "=== 配置检查 ===\n";
    foreach ($config as $field) {
        if ($field['type'] === 'edittable') {
            echo "找到 edittable 字段: " . $field['field'] . "\n";
            
            if (isset($field['config']['externalLinkage'])) {
                echo "✅ 外部联动配置存在\n";
                echo "监听字段: " . implode(', ', $field['config']['externalLinkage']['triggerFields']) . "\n";
                
                $targetFields = $field['config']['externalLinkage']['rules']['visibility']['targetFields'];
                echo "目标字段数量: " . count($targetFields) . "\n";
                
                foreach ($targetFields as $target) {
                    echo "- " . $target['field'] . "\n";
                }
            } else {
                echo "❌ 外部联动配置缺失\n";
            }
            
            // 检查 form 配置中是否包含目标字段
            $formFields = array_column($field['config']['form'], 'field');
            echo "表单字段: " . implode(', ', $formFields) . "\n";
            
            $targetFieldNames = ['unit_pirce', 'foreign_currency_unit_pirce'];
            foreach ($targetFieldNames as $targetField) {
                if (in_array($targetField, $formFields)) {
                    echo "✅ 目标字段 $targetField 存在于表单配置中\n";
                } else {
                    echo "❌ 目标字段 $targetField 不存在于表单配置中\n";
                }
            }
        }
    }
    
    return $config;
}

// 执行调试
$debugConfig = debugLinkageConfig();

// 返回配置给前端
header('Content-Type: application/json');
echo json_encode([
    'code' => 200,
    'message' => 'success',
    'data' => $debugConfig,
    'debug' => [
        'timestamp' => date('Y-m-d H:i:s'),
        'note' => '请检查浏览器控制台的 [EditTable外部联动] 日志'
    ]
]);
?>
