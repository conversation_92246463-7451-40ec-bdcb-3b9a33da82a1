/**
 * 日期格式化测试工具
 * 用于测试日期格式化功能是否正常工作
 */

/**
 * 格式化日期为 YYYY-MM-DD 格式
 * @param date 日期值（可能是 Date 对象、ISO 字符串或其他格式）
 * @returns 格式化后的日期字符串或原值
 */
export const formatDateValue = (date: any): any => {
  if (!date) return date;
  
  // 如果是字符串且已经是 YYYY-MM-DD 格式，直接返回
  if (typeof date === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(date)) {
    return date;
  }
  
  // 尝试转换为 Date 对象
  let dateObj: Date;
  if (date instanceof Date) {
    dateObj = date;
  } else if (typeof date === 'string') {
    dateObj = new Date(date);
  } else {
    return date; // 无法处理的类型，返回原值
  }
  
  // 检查是否是有效日期
  if (Number.isNaN(dateObj.getTime())) {
    return date; // 无效日期，返回原值
  }
  
  // 格式化为 YYYY-MM-DD
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

/**
 * 测试日期格式化功能
 */
export const testDateFormatting = () => {
  console.log('=== 日期格式化测试 ===');
  
  const testCases = [
    // ISO 字符串格式
    '2025-07-30T09:07:33.600Z',
    '2024-12-25T15:30:00.000Z',
    
    // Date 对象
    new Date('2025-07-30'),
    new Date(),
    
    // 已经是正确格式的字符串
    '2024-01-01',
    '2025-12-31',
    
    // 其他格式的字符串
    '2024/01/01',
    '01/01/2024',
    
    // 无效值
    null,
    undefined,
    '',
    'invalid-date',
    123456,
  ];
  
  testCases.forEach((testCase, index) => {
    const result = formatDateValue(testCase);
    console.log(`测试 ${index + 1}:`, {
      input: testCase,
      inputType: typeof testCase,
      output: result,
      outputType: typeof result,
    });
  });
  
  console.log('=== 测试完成 ===');
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).testDateFormatting = testDateFormatting;
  console.log('日期格式化测试函数已添加到 window.testDateFormatting，可在控制台中调用');
}
