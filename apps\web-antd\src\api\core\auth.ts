import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    accessToken: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>('/user/login', data);
}
/**
 * 判断是否跳过 token refresh
 */
function shouldSkipTokenRefresh(): boolean {
  // 优先检查明确的环境变量配置
  const skipTokenRefresh = import.meta.env.VITE_SKIP_TOKEN_REFRESH;
  if (skipTokenRefresh === 'true') {
    return true;
  }

  // 如果没有明确配置，检查代理目标是否为本地地址
  const proxyTarget = import.meta.env.VITE_PROXY_TARGET;
  return (
    proxyTarget &&
    (proxyTarget.includes('localhost') ||
      proxyTarget.includes('127.0.0.1') ||
      proxyTarget.includes('0.0.0.0'))
  );
}

/**
 * 判断是否从auth登录
 */
export async function refreshToken() {
  // 如果配置跳过 token refresh 接口
  if (shouldSkipTokenRefresh()) {
    console.warn('[Auth] 跳过 token refresh 接口');
    return { data: 'skipped', status: 200 };
  }

  return requestClient.get('/user/token/refresh');
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return requestClient.post<AuthApi.RefreshTokenResult>('/auth/refresh', {
    withCredentials: true,
  });
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return baseRequestClient.post('/auth/logout', {
    withCredentials: true,
  });
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  return requestClient.get<string[]>('/auth/codes');
}
