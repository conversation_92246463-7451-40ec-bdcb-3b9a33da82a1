VITE_BASE=/tests2/

# 接口地址
VITE_GLOB_API_URL=https://erp.gbuilderchina.com/testapiv2/

# 是否开启压缩，可以设置为 none, brotli, gzip
VITE_COMPRESS=none

# 是否开启 PWA
VITE_PWA=false

# vue-router 的模式
VITE_ROUTER_HISTORY=hash

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true

# 打包后是否生成dist.zip
VITE_ARCHIVER=false

# ================================
# 代理配置 - 测试环境
# ================================

# 代理目标地址 - 测试服务器
VITE_PROXY_TARGET=https://erp.gbuilderchina.com/testapiv2/

# 代理路径前缀
VITE_PROXY_PATH_PREFIX=/api

# 路径重写规则 - 测试环境不需要重写路径
VITE_PROXY_REWRITE_FROM=^/api
VITE_PROXY_REWRITE_TO=

# 代理选项
VITE_PROXY_CHANGE_ORIGIN=true
VITE_PROXY_WS=true
