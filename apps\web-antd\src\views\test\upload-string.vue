<template>
  <div class="p-4">
    <h2 class="mb-4 text-lg font-semibold">Upload 组件字符串值测试</h2>
    
    <!-- 表单测试 -->
    <VbenForm
      :schema="formSchema"
      @submit="handleSubmit"
      class="mb-8"
    />
    
    <!-- 当前表单值显示 -->
    <div class="p-4 bg-gray-100 rounded">
      <h3 class="mb-2 font-medium">当前表单值：</h3>
      <pre>{{ JSON.stringify(formValues, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { VbenForm } from '#/adapter/form';
import type { VbenFormSchema } from '#/adapter/form';

// 表单值
const formValues = ref({});

// 表单配置
const formSchema: VbenFormSchema[] = [
  {
    fieldName: 'avatar',
    label: '头像上传',
    component: 'Upload',
    componentProps: {
      accept: '.png,.jpg,.jpeg',
      maxCount: 1,
      multiple: false,
      listType: 'picture-card',
      showUploadList: true,
    },
    defaultValue: '',
  },
  {
    fieldName: 'documents',
    label: '文档上传',
    component: 'Upload',
    componentProps: {
      accept: '.pdf,.doc,.docx',
      maxCount: 3,
      multiple: true,
      listType: 'text',
      showUploadList: true,
    },
    defaultValue: '',
  },
];

// 监听表单值变化
watch(
  () => formValues.value,
  (newValues) => {
    console.log('表单值变化:', newValues);
  },
  { deep: true }
);

// 表单提交处理
function handleSubmit(values: Record<string, any>) {
  console.log('表单提交:', values);
  formValues.value = values;
}
</script>
</template>
