<template>
  <div class="p-4">
    <h2 class="mb-4 text-lg font-semibold">Upload 组件字符串值测试</h2>
    
    <!-- 表单测试 -->
    <VbenForm
      :schema="formSchema"
      @submit="handleSubmit"
      class="mb-8"
    />
    
    <!-- 当前表单值显示 -->
    <div class="p-4 bg-gray-100 rounded">
      <h3 class="mb-2 font-medium">当前表单值：</h3>
      <pre>{{ JSON.stringify(formValues, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { VbenForm } from '#/adapter/form';
import type { VbenFormSchema } from '#/adapter/form';
import { transformBackendSearchToSchema } from '#/utils/search-schema';

// 表单值
const formValues = ref({});

// 模拟后端数据
const backendFields = [
  {
    field: 'avatar',
    title: '头像上传（单文件）',
    type: 'Upload',
    config: {
      accept: '.png,.jpg,.jpeg',
      maxCount: 1,
      multiple: false,
      listType: 'picture-card',
      showUploadList: true,
    },
  },
  {
    field: 'documents',
    title: '文档上传（多文件）',
    type: 'Upload',
    config: {
      accept: '.pdf,.doc,.docx',
      maxCount: 3,
      multiple: true,
      listType: 'text',
      showUploadList: true,
    },
  },
  {
    field: 'existingImage',
    title: '已有图片（编辑测试）',
    type: 'Upload',
    default: 'https://via.placeholder.com/150x150.png?text=Test+Image',
    config: {
      accept: '.png,.jpg,.jpeg',
      maxCount: 1,
      multiple: false,
      listType: 'picture-card',
      showUploadList: true,
    },
  },
];

// 使用 transformBackendSearchToSchema 转换
const formSchema = computed(() => {
  const schema = transformBackendSearchToSchema(backendFields);
  console.log('转换后的 schema:', schema);
  return schema;
});

// 监听表单值变化
watch(
  () => formValues.value,
  (newValues) => {
    console.log('表单值变化:', newValues);
  },
  { deep: true }
);

// 表单提交处理
function handleSubmit(values: Record<string, any>) {
  console.log('表单提交:', values);
  console.log('表单提交类型检查:', {
    avatar: typeof values.avatar,
    documents: typeof values.documents,
    existingImage: typeof values.existingImage,
  });
  formValues.value = values;
}
</script>
</template>
