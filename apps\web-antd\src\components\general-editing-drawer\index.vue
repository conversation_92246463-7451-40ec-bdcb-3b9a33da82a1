<script setup lang="ts">
import { nextTick, ref, provide } from 'vue';

import { useVbenDrawer, useVbenForm } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import {
  COMPONENT_INIT_DELAYS,
  createDelay,
  DEFAULT_FORM_CONFIG,
  extractDefaultValues,
  findFieldsToRestore,
  getSchemaFieldNames,
  handleError,
  processCreateModeFields,
  processFormData,
  RangePickerConfigGenerator,
} from '#/components/general-editing-drawer/datas/data';

import { filterFormData } from '#/utils/form/filterFormData';
import {
  setFormBatchSetting,
  transformBackendSearchToSchema,
} from '#/utils/search-schema';

// 使用优化后的类型定义
// interface DrawerData 已在 data.ts 中定义

const emit = defineEmits(['confirm']);
const api = ref<any>(null);
const processedParams = ref<Record<string, any>>({});
const editMode = ref<'create' | 'edit'>('create');
const currentRow = ref<any>(null);
const schema = ref<any[]>([]);

const fieldMappingTimeArray = ref<any[]>([]);

// 重置所有状态的函数
const resetAllStates = () => {
  api.value = null;
  processedParams.value = {};
  editMode.value = 'create';
  currentRow.value = null;
  schema.value = [];
  fieldMappingTimeArray.value = [];
};
// 数据回显处理 - 使用优化后的处理器
const processFormDataForEcho = (
  formData: Record<string, any>,
  schema: any[],
) => {
  return processFormData(formData, schema);
};

const [Drawer, drawerApi] = useVbenDrawer({
  onOpenChange: async (isOpen: boolean) => {
    if (isOpen) {
      try {
        // 重置所有状态
        resetAllStates();

        const { schema, apiFn, params, mode, row } =
          drawerApi.getData<Record<string, any>>();
        drawerApi.setState({
          loading: true,
        });

        // 设置 API 和表单数据
        api.value = apiFn;

        // 使用外部传入的模式，如果没有传入则使用默认逻辑
        editMode.value = mode || 'create';

        // 使用外部已处理的 params，如果没有则使用默认逻辑
        processedParams.value = params || {};

        let formdatas: any = { main: { data: {} } };

        // 只有编辑模式才发起详情请求
        if (editMode.value === 'edit') {
          // 编辑模式：获取详情数据
          const detailResponse = await apiFn.detail(params);

          formdatas = detailResponse?.formdatas || { main: { data: {} } };
          currentRow.value = formdatas?.main?.data || {};
        } else {
          // 新增模式：跳过详情请求，使用空数据
          currentRow.value = row || {};
          // 确保 formdatas 结构完整
          formdatas = { main: { data: currentRow.value } };
        }
        formApi.resetForm();
        // 尝试多种可能的数据路径
        let rawData = null;
        // 尝试路径1: schema[0].dataItem
        if (schema) {
          rawData = schema;
        }
        // API value

        if (rawData && Array.isArray(rawData) && rawData.length > 0) {
          const result = transformBackendSearchToSchema(rawData, {
            formMode: editMode.value === 'create' ? 'add' : 'edit',
          });
          // console.log('转换后的schema', result);

          // 生成 fieldMappingTimeArray - 使用优化后的生成器
          fieldMappingTimeArray.value =
            RangePickerConfigGenerator.generateFieldMappingArray(result);

          // 在创建模式下，确保表单字段不被禁用 - 使用优化后的处理函数
          schema.value =
            editMode.value === 'create'
              ? processCreateModeFields(result)
              : result;

          // 智能二次赋值策略：先设置schema，再设置数据，最后恢复被联动清空的字段
          formApi.setState({
            schema: result,
            fieldMappingTime: fieldMappingTimeArray.value,
          });

          // 等待 API 组件加载完成
          await nextTick();

          // 首先设置所有字段的默认值（包括隐藏字段） - 使用优化后的提取函数
          const defaultValues = extractDefaultValues(result);

          // 标记开始批量设置（回显数据）
          setFormBatchSetting(formApi, true);

          // 设置默认值
          if (Object.keys(defaultValues).length > 0) {
            await formApi.setValues(defaultValues);
          }

          // 如果有编辑数据，则覆盖默认值
          if (
            formdatas?.main?.data &&
            Object.keys(formdatas.main.data).length > 0
          ) {
            // 处理表单字段的数据回显（包括 edittable 和 TreeSelect 多选）
            const processedData = processFormDataForEcho(
              formdatas.main.data,
              result,
            );

            // 开始设置表单数据，保持 loading 状态

            // 第一次：设置原始数据（会触发联动逻辑）
            // filterFields=true 确保只设置 schema 中定义的字段

            await formApi.setValues(processedData);

            // 第二次：延迟检查并恢复被联动清空的字段 - 使用优化后的处理函数
            await createDelay(COMPONENT_INIT_DELAYS.FIELD_RESTORE);
            try {
              const currentValues = await formApi.getValues();
              const currentSchema = formApi.state?.schema || [];
              const schemaFieldNames = getSchemaFieldNames(currentSchema);
              const fieldsToRestore = findFieldsToRestore(
                formdatas?.main?.data || {},
                currentValues,
                schemaFieldNames,
              );

              // 恢复被清空的字段
              if (Object.keys(fieldsToRestore).length > 0) {
                // 恢复被联动清空的字段
                // 对恢复的数据也进行表单字段处理
                const processedRestoreData = processFormDataForEcho(
                  fieldsToRestore,
                  result,
                );

                // 使用 setValues 方法批量设置，确保字段过滤
                await formApi.setValues(processedRestoreData);
              }
            } catch (error) {
              // 恢复字段时出错
              handleError(error, '恢复字段时出错');
            }
          }
        } else {
          // rawData 不满足条件，跳过转换
          // 即使没有 schema 转换，也需要处理表单字段数据
          const processedData = processFormDataForEcho(
            formdatas?.main?.data || {},
            [],
          );
          // filterFields=true 确保只设置 schema 中定义的字段
          await formApi.setValues(processedData);
        }

        // 额外等待一段时间，确保所有组件（特别是 EditTable）完全初始化
        // 等待组件完全初始化... - 使用优化后的延迟函数
        await createDelay(COMPONENT_INIT_DELAYS.FULL_INIT);

        // 标记批量设置完成
        setFormBatchSetting(formApi, false);

        // 所有数据设置完成，关闭 loading
        drawerApi.setState({
          loading: false,
        });
      } catch (error) {
        // 标记批量设置完成（即使出错也要清除状态）
        setFormBatchSetting(formApi, false);

        // 处理错误，取消 loading 状态
        drawerApi.setState({
          loading: false,
        });

        // 显示错误信息
        const errorMessage = handleError(error, '加载数据失败');
        message.error(`加载失败：${errorMessage}`);

        // 关闭抽屉
        drawerApi.close();
      }
    } else {
      // 抽屉关闭时重置状态
      resetAllStates();
    }
  },
  onConfirm: async () => {
    await formApi.validate();
    const rawFromData = await formApi.getValues();

    // 筛选有值的数据
    const fromData = filterFormData(rawFromData);

    if (Object.keys(fromData).length === 0) {
      // 没有有效的表单数据可提交
    }

    // 合并表单数据和处理后的 params
    const submitData = {
      ...fromData,
      ...processedParams.value,
    };

    try {
      drawerApi.setState({
        confirmLoading: true,
      });

      // 统一使用 create 方法，通过是否有 id 来区分新增和编辑
      if (api.value?.create) {
        // 编辑模式时，id 来自 processedParams，不是来自表单数据
        // submitData 已经包含了 fromData 和 processedParams 的合并结果
        await api.value.create(submitData);
      } else {
        throw new Error('API 方法不可用：缺少 create 方法');
      }

      drawerApi.close();
      emit('confirm');
      drawerApi.setState({
        confirmLoading: false,
      });
      // 提交成功后重置状态
      resetAllStates();
    } catch (error) {
      drawerApi.setState({
        confirmLoading: false,
      });

      // 提供更友好的错误信息
      const errorMessage = handleError(error, '提交数据时发生错误');

      // 显示用户友好的错误提示
      message.error(`提交失败：${errorMessage}`);
    }
  },
  onCancel: async () => {
    drawerApi.close();
    // 取消时重置状态
    resetAllStates();
  },
});

// 创建响应式的表单值
const formValues = ref({});

const [BaseForm, formApi] = useVbenForm({
  // 使用默认表单配置
  ...DEFAULT_FORM_CONFIG,
  // 设置字段映射时间数组
  fieldMappingTime: fieldMappingTimeArray.value,
  // 添加表单值变化监听
  handleValuesChange: (allValues: any, changedFields: string[]) => {
    // 更新响应式的表单值
    formValues.value = { ...allValues };
    console.log('[general-editing-drawer] 表单值变化:', {
      changedFields,
      allValues: formValues.value,
    });
  },
});

// 提供表单值给子组件，用于外部联动
provide('formValues', formValues);
</script>
<template>
  <Drawer>
    <BaseForm />
  </Drawer>
</template>

<style scoped>
.edittable-debug {
  padding: 16px;
  margin: 8px 0;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.edittable-debug > div {
  margin-bottom: 8px;
}

.edittable-debug > div:last-child {
  margin-bottom: 0;
}
</style>
