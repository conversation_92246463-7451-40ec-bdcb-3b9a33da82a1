/**
 * 字符串值与文件对象转换工具
 */

/**
 * 将字符串 URL 转换为 Upload 组件的文件对象
 * @param url 文件 URL 字符串
 * @param index 文件索引（用于生成唯一 uid）
 * @returns 文件对象
 */
export function stringToFileObject(url: string, index: number = 0) {
  if (!url || typeof url !== 'string') {
    return null;
  }

  // 从 URL 中提取文件名
  const fileName = url.split('/').pop() || `文件${index + 1}`;

  return {
    uid: `upload-${Date.now()}-${index}`,
    name: fileName,
    status: 'done',
    url: url,
    response: url, // 保存原始响应字符串
    thumbUrl: url, // 如果是图片，用于预览
  };
}

/**
 * 将多个字符串 URL（逗号分隔）转换为文件对象数组
 * @param urls 逗号分隔的 URL 字符串
 * @returns 文件对象数组
 */
export function stringToFileList(urls: string): any[] {
  if (!urls || typeof urls !== 'string') {
    return [];
  }

  // 分割字符串并过滤空值
  const urlArray = urls.split(',').filter((url) => url.trim());

  return urlArray
    .map((url, index) => stringToFileObject(url.trim(), index))
    .filter(Boolean);
}

/**
 * 从文件对象数组中提取字符串值
 * @param fileList 文件对象数组
 * @param multiple 是否多文件
 * @returns 字符串值（单文件）或逗号分隔的字符串（多文件）
 */
export function fileListToString(
  fileList: any[],
  multiple: boolean = false,
): string {
  if (!Array.isArray(fileList)) {
    return '';
  }

  // 处理已完成的文件
  const completedFiles = fileList.filter((file) => {
    // 检查文件状态和响应数据
    return file.status === 'done' && (file.response || file.url);
  });

  if (completedFiles.length === 0) {
    return '';
  }

  // 提取字符串值
  const stringValues = completedFiles
    .map((file) => {
      // 优先使用 response，其次使用 url
      if (file.response) {
        // 如果 response 是字符串，直接使用
        if (typeof file.response === 'string') {
          return file.response;
        }
        // 如果 response 是对象，尝试提取 data、url 等字段
        if (typeof file.response === 'object') {
          return (
            file.response.data ||
            file.response.url ||
            file.response.path ||
            file.response.src ||
            ''
          );
        }
      }
      // 使用文件的 url 属性
      return file.url || '';
    })
    .filter(Boolean);

  if (multiple) {
    // 多文件：返回逗号分隔的字符串
    return stringValues.join(',');
  } else {
    // 单文件：返回第一个值
    return stringValues[0] || '';
  }
}

/**
 * 判断是否为图片文件
 * @param url 文件 URL
 * @returns 是否为图片
 */
export function isImageFile(url: string): boolean {
  if (!url) return false;

  const imageExtensions = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.bmp',
    '.webp',
    '.svg',
  ];
  const lowerUrl = url.toLowerCase();

  return imageExtensions.some((ext) => lowerUrl.includes(ext));
}

/**
 * 为 Upload 组件创建字符串值处理器
 * @param multiple 是否多文件上传
 * @returns onChange 处理函数
 */
export function createStringValueHandler(multiple: boolean = false) {
  return (info: any) => {
    const { fileList } = info;
    return fileListToString(fileList, multiple);
  };
}
