# 日期格式化修复说明

## 问题描述

在使用 EditTable 组件的表单功能时，日期选择框（DatePicker）返回的值不是标准的 `YYYY-MM-DD` 格式，而是 ISO 8601 格式的字符串，如：`2025-07-30T09:07:33.600Z`。

这会导致以下问题：
1. 数据显示不一致
2. 后端接收到的日期格式可能不符合预期
3. 数据导入导出时格式混乱

## 解决方案

### 1. 添加日期格式化工具函数

在 `EditTable.vue` 中添加了 `formatDateValue` 函数：

```typescript
const formatDateValue = (date: any): any => {
  if (!date) return date;
  
  // 如果是字符串且已经是 YYYY-MM-DD 格式，直接返回
  if (typeof date === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(date)) {
    return date;
  }
  
  // 尝试转换为 Date 对象
  let dateObj: Date;
  if (date instanceof Date) {
    dateObj = date;
  } else if (typeof date === 'string') {
    dateObj = new Date(date);
  } else {
    return date; // 无法处理的类型，返回原值
  }
  
  // 检查是否是有效日期
  if (Number.isNaN(dateObj.getTime())) {
    return date; // 无效日期，返回原值
  }
  
  // 格式化为 YYYY-MM-DD
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};
```

### 2. 添加表单数据处理函数

添加了 `processFormDateFields` 函数，用于批量处理表单中的日期字段：

```typescript
const processFormDateFields = (values: any, formConfig: any[] = []) => {
  const processedValues = { ...values };
  
  // 遍历表单配置，找出日期类型的字段
  formConfig.forEach((field: any) => {
    const fieldName = field.field || field.fieldName;
    if (!fieldName || !processedValues[fieldName]) return;
    
    // 检查是否是日期类型字段
    const isDateField = 
      field.component === 'DatePicker' ||
      field.type === 'date' ||
      field.type === 'datetime' ||
      fieldName.toLowerCase().includes('date') ||
      fieldName.toLowerCase().includes('time');
    
    if (isDateField) {
      processedValues[fieldName] = formatDateValue(processedValues[fieldName]);
    }
  });
  
  return processedValues;
};
```

### 3. 在关键位置应用日期格式化

#### 3.1 表单提交时（新增/编辑）

在 `handleModalConfirm` 函数中：

```typescript
// 处理表单数据中的日期字段
const formConfig = props.params?.form || [];
const processedValues = processFormDateFields(values, formConfig);

// 使用处理后的数据
const updatedRowData = {
  ...processedValues, // 表单提交的新数据（已处理日期格式）
  id: originalRow.id,
  _customKey: originalRow._customKey,
};
```

#### 3.2 数据导入时

在 `transformImportData` 函数中：

```typescript
// 根据表格列配置映射字段
importableColumns.forEach((column: any, columnIndex: number) => {
  const fieldName = column.field;
  if (fieldName && row[columnIndex] !== undefined) {
    let value = row[columnIndex];
    
    // 如果是日期类型字段，格式化日期值
    if (column.type === 'date' || column.type === 'datetime') {
      value = formatDateValue(value);
    }
    
    transformedRow[fieldName] = value;
  }
});
```

## 支持的日期格式识别

### 自动识别的日期字段类型

1. **表单配置中**：
   - `component === 'DatePicker'`
   - `type === 'date'`
   - `type === 'datetime'`
   - 字段名包含 'date' 或 'time'

2. **列配置中**：
   - `type === 'date'`
   - `type === 'datetime'`

### 支持的输入格式

- ISO 8601 格式：`2025-07-30T09:07:33.600Z`
- Date 对象：`new Date()`
- 标准日期字符串：`2024-01-01`
- 其他可解析的日期格式

### 输出格式

统一输出为：`YYYY-MM-DD` 格式

## 使用示例

### 表单配置

```typescript
const formConfig = [
  {
    field: 'createTime',
    title: '创建时间',
    component: 'DatePicker',
    config: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD', // 这个设置可以减少格式转换的需要
    },
  },
];
```

### 列配置

```typescript
const columns = [
  {
    field: 'createTime',
    title: '创建时间',
    width: 160,
    type: 'date', // 标记为日期类型
  },
];
```

## 测试

可以使用提供的测试工具进行验证：

```typescript
import { testDateFormatting } from '#/utils/date-format-test';

// 在浏览器控制台中运行
testDateFormatting();
```

## 注意事项

1. **时区处理**：当前实现会将所有日期转换为本地时区的日期，如果需要 UTC 时间，需要额外处理
2. **性能考虑**：日期格式化会在每次表单提交和数据导入时执行，对于大量数据可能有性能影响
3. **兼容性**：保持了对原有数据格式的兼容，如果数据已经是正确格式则不会重复处理

## 影响范围

- ✅ 表单新增/编辑功能
- ✅ Excel 数据导入功能
- ✅ 数据显示一致性
- ✅ 后端数据接收格式统一
