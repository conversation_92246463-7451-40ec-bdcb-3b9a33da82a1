# 代理配置系统

## 概述

本项目实现了完全基于环境变量的代理配置系统，支持：

- ✅ 多环境配置（本地、测试、预发布、生产）
- ✅ 多个代理目标支持
- ✅ 灵活的路径重写规则
- ✅ 便捷的环境切换脚本
- ✅ 零配置文件修改

## 快速开始

### 1. 使用预设环境

```bash
# 切换到本地开发环境并启动
npm run dev:local

# 切换到测试环境并启动
npm run dev:test

# 切换到预发布环境并启动
npm run dev:staging
```

### 2. 手动切换环境

```bash
# 切换到指定环境（不启动服务）
npm run proxy:local     # 本地环境
npm run proxy:test      # 测试环境
npm run proxy:staging   # 预发布环境
npm run proxy:production # 生产环境

# 然后手动启动开发服务器
npm run dev
```

### 3. 查看帮助信息

```bash
npm run proxy:switch
```

## 环境配置

### 预设环境

| 环境 | 目标地址 | 路径重写 |
|------|----------|----------|
| local | `http://127.0.0.1:8000` | `/api` → `/apiv2` |
| test | `https://erp.gbuilderchina.com/testapiv2/` | `/api` → `` |
| staging | `https://erp-staging.gbuilderchina.com/apiv2/` | `/api` → `` |
| production | `https://erp.gbuilderchina.com/apiv2/` | `/api` → `` |

### 自定义配置

在 `.env.local` 文件中添加自定义配置：

```bash
# 基础代理配置
VITE_PROXY_TARGET=http://your-api-server.com
VITE_PROXY_PATH_PREFIX=/api
VITE_PROXY_REWRITE_FROM=^/api
VITE_PROXY_REWRITE_TO=/v1
VITE_PROXY_CHANGE_ORIGIN=true
VITE_PROXY_WS=true

# 多个代理配置
VITE_PROXY_ADDITIONAL_1_TARGET=http://file-server.com
VITE_PROXY_ADDITIONAL_1_PREFIX=/upload
VITE_PROXY_ADDITIONAL_1_REWRITE_FROM=^/upload
VITE_PROXY_ADDITIONAL_1_REWRITE_TO=/files

VITE_PROXY_ADDITIONAL_2_TARGET=ws://websocket-server.com
VITE_PROXY_ADDITIONAL_2_PREFIX=/ws
VITE_PROXY_ADDITIONAL_2_REWRITE_FROM=^/ws
VITE_PROXY_ADDITIONAL_2_REWRITE_TO=/socket
```

## 文件结构

```
apps/web-antd/
├── proxy-manager.ts          # 代理配置管理器
├── vite.config.mts           # Vite 配置文件
├── scripts/
│   └── switch-proxy.js       # 环境切换脚本
├── .env.example              # 环境变量示例
├── .env.development          # 开发环境配置
├── .env.test                 # 测试环境配置
└── .env.local                # 本地配置（Git 忽略）
```

## 配置参数

### 基础参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `VITE_PROXY_TARGET` | 代理目标地址（必填） | - |
| `VITE_PROXY_PATH_PREFIX` | 代理路径前缀 | `/api` |
| `VITE_PROXY_REWRITE_FROM` | 重写规则：匹配模式 | `^/api` |
| `VITE_PROXY_REWRITE_TO` | 重写规则：替换内容 | `/apiv2` |
| `VITE_PROXY_CHANGE_ORIGIN` | 是否改变源地址 | `true` |
| `VITE_PROXY_WS` | 是否支持 WebSocket | `true` |

### 多代理参数

使用 `VITE_PROXY_ADDITIONAL_N_*` 格式配置多个代理：

```bash
VITE_PROXY_ADDITIONAL_1_TARGET=http://api2.example.com
VITE_PROXY_ADDITIONAL_1_PREFIX=/api2
VITE_PROXY_ADDITIONAL_1_REWRITE_FROM=^/api2
VITE_PROXY_ADDITIONAL_1_REWRITE_TO=/v1
```

## 使用示例

### 开发流程

1. **启动本地后端服务**（如果有）
2. **选择环境并启动前端**：
   ```bash
   npm run dev:local  # 连接本地后端
   # 或
   npm run dev:test   # 连接测试服务器
   ```

### 环境切换

```bash
# 当前在本地环境开发，需要切换到测试环境
npm run proxy:test
npm run dev
```

### 调试代理

启动开发服务器时，控制台会显示代理配置信息：

```
[代理配置] 最终配置: {
  '/api': {
    target: 'http://127.0.0.1:8000',
    changeOrigin: true,
    ws: true,
    rewrite: [Function]
  }
}
```

## 注意事项

1. **环境文件优先级**：`.env.local` > `.env.[mode]` > `.env`
2. **重启服务**：修改环境变量后需要重启开发服务器
3. **Git 管理**：`.env.local` 不会被提交到 Git
4. **安全性**：不要在环境文件中存储敏感信息

## 故障排除

### 代理不生效

1. 检查 `VITE_PROXY_TARGET` 是否正确配置
2. 确认目标服务器是否可访问
3. 查看控制台的代理配置日志

### 路径重写问题

1. 检查 `VITE_PROXY_REWRITE_FROM` 正则表达式是否正确
2. 确认 `VITE_PROXY_REWRITE_TO` 替换规则是否符合预期
3. 使用浏览器开发者工具查看实际请求路径

### 环境切换失败

1. 确认 Node.js 版本支持
2. 检查 `scripts/switch-proxy.js` 文件权限
3. 手动编辑 `.env.local` 文件作为备选方案
