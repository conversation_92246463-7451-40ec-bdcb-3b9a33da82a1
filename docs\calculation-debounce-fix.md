# 表单计算防抖修复说明

## 问题描述

在表单计算功能中，当用户输入太快时，可能会出现以下问题：

1. **计算结果为空**：快速输入导致计算被中断或跳过
2. **递归更新错误**：计算字段更新自身导致无限循环
3. **性能问题**：频繁的计算触发影响用户体验

## 修复方案

### 1. 防抖机制

添加了 150ms 的防抖延迟，确保用户停止输入后才执行计算：

```typescript
// 防抖计算的存储对象
const calculationDebounceMap = new Map<string, NodeJS.Timeout>();

// 计算队列，用于处理快速连续的计算请求
const calculationQueue = new Map<string, {
  values: Record<string, any>;
  formApi: any;
  calculationRule: LinkageCalculationRule;
  timestamp: number;
}>();
```

### 2. 计算队列

使用队列机制处理快速连续的计算请求，确保只执行最新的计算：

```typescript
executeCalculation(calculationRule, values, formApi) {
  // 清除之前的计算定时器
  if (calculationDebounceMap.has(debounceKey)) {
    clearTimeout(calculationDebounceMap.get(debounceKey)!);
  }

  // 将当前计算请求加入队列
  calculationQueue.set(fieldName, {
    values: { ...values }, // 深拷贝避免引用问题
    formApi,
    calculationRule,
    timestamp: Date.now(),
  });

  // 设置防抖延迟执行
  const debounceTimeout = setTimeout(() => {
    this.executeCalculationImmediate(fieldName);
  }, 150); // 150ms 防抖延迟
}
```

### 3. 增强的数值解析

改进了数值解析逻辑，更好地处理用户输入过程中的中间状态：

```typescript
parseNumber(value: any): number {
  // 处理 null、undefined、空字符串
  if (value === null || value === undefined || value === '') {
    return 0;
  }

  // 如果是字符串
  if (typeof value === 'string') {
    const trimmed = value.trim();
    
    // 处理正在输入的数字（如 "123."、"-"、"12.3"）
    if (trimmed === '' || trimmed === '-' || trimmed === '.') {
      return 0;
    }

    const parsed = parseFloat(trimmed);
    return isNaN(parsed) || !isFinite(parsed) ? 0 : parsed;
  }

  return isNaN(num) || !isFinite(num) ? 0 : num;
}
```

### 4. 递归防护

添加了多层递归防护机制：

1. **计算锁**：防止同一字段的重复计算
2. **触发字段过滤**：确保触发字段不包含计算字段本身
3. **值变化检测**：只有当计算结果与当前值不同时才更新

```typescript
// 过滤触发字段，确保不包含计算字段本身（防止递归）
const filteredTriggerFields = (config.calculation.triggerFields || []).filter(
  (triggerField: string) => triggerField !== item.field
);

// 设置计算结果 - 只有当结果与当前值不同时才更新
const currentValue = formApi.getFieldValue?.(formApi.fieldName);
if (currentValue !== result) {
  formApi.setFieldValue(formApi.fieldName, result);
}
```

## 使用示例

### 正确的配置

```json
{
    "field": "total_amount",
    "title": "总金额",
    "type": "number",
    "config": {
        "readonly": true,
        "prefix": "¥",
        "calculation": {
            "type": "sum",
            "tableFields": ["amount"],
            "triggerFields": ["items"],
            "precision": 2,
            "defaultValue": 0,
            "realtime": true
        }
    }
}
```

### 关键要点

1. **`triggerFields`** 不能包含计算字段本身
2. **`tableFields`** 指定要计算的表格列名
3. **`triggerFields`** 指定存储表格数据的表单字段名

## 测试场景

### 1. 快速输入测试
- 在数字输入框中快速输入数字
- 验证计算结果正确且不为空

### 2. 表格数据变化测试
- 快速添加/删除表格行
- 快速修改表格中的数值
- 验证总计字段实时更新且准确

### 3. 边界情况测试
- 输入空值、负数、小数
- 输入非数字字符
- 验证解析和计算的健壮性

## 性能优化

1. **防抖延迟**：150ms 平衡了响应性和性能
2. **队列管理**：避免重复计算，只执行最新请求
3. **深拷贝优化**：避免引用问题导致的计算错误
4. **内存清理**：及时清理计算锁和队列项

## 兼容性

- 向后兼容现有的计算配置
- 不影响其他表单功能
- 支持所有计算类型（sum、average、max、min等）

## 注意事项

1. 防抖延迟可能会让用户感觉响应稍慢，但能确保计算准确性
2. 在高频操作场景下，建议适当增加防抖延迟时间
3. 确保表格数据字段名配置正确，避免计算无法触发
