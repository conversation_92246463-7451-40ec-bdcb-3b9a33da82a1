# 代理配置指南

## 概述

本项目支持完全基于环境变量的代理配置，无需修改 `vite.config.mts` 文件即可灵活切换不同的代理环境。

## 基础配置

### 必需配置

```bash
# 代理目标地址（必填）
VITE_PROXY_TARGET=http://127.0.0.1:8000
```

### 可选配置

```bash
# 代理路径前缀（默认：/api）
VITE_PROXY_PATH_PREFIX=/api

# 路径重写规则
VITE_PROXY_REWRITE_FROM=^/api
VITE_PROXY_REWRITE_TO=/apiv2

# 是否改变源地址（默认：true）
VITE_PROXY_CHANGE_ORIGIN=true

# 是否支持 WebSocket（默认：true）
VITE_PROXY_WS=true
```

## 多环境配置示例

### 本地开发环境

```bash
# .env.local
VITE_PROXY_TARGET=http://127.0.0.1:8000
VITE_PROXY_REWRITE_FROM=^/api
VITE_PROXY_REWRITE_TO=/apiv2
```

### 测试环境

```bash
# .env.test
VITE_PROXY_TARGET=https://erp.gbuilderchina.com/testapiv2/
VITE_PROXY_REWRITE_FROM=^/api
VITE_PROXY_REWRITE_TO=
```

### 预发布环境

```bash
# .env.staging
VITE_PROXY_TARGET=https://erp-staging.gbuilderchina.com/apiv2/
VITE_PROXY_REWRITE_FROM=^/api
VITE_PROXY_REWRITE_TO=
```

### 生产环境

```bash
# .env.production
VITE_PROXY_TARGET=https://erp.gbuilderchina.com/apiv2/
VITE_PROXY_REWRITE_FROM=^/api
VITE_PROXY_REWRITE_TO=
```

## 多个代理配置

支持配置多个代理，用于不同的服务：

```bash
# 主 API 代理
VITE_PROXY_TARGET=http://127.0.0.1:8000
VITE_PROXY_PATH_PREFIX=/api

# 文件上传服务代理
VITE_PROXY_ADDITIONAL_1_TARGET=http://localhost:3001
VITE_PROXY_ADDITIONAL_1_PREFIX=/upload
VITE_PROXY_ADDITIONAL_1_REWRITE_FROM=^/upload
VITE_PROXY_ADDITIONAL_1_REWRITE_TO=/files

# WebSocket 服务代理
VITE_PROXY_ADDITIONAL_2_TARGET=ws://localhost:3002
VITE_PROXY_ADDITIONAL_2_PREFIX=/ws
VITE_PROXY_ADDITIONAL_2_REWRITE_FROM=^/ws
VITE_PROXY_ADDITIONAL_2_REWRITE_TO=/socket

# 第三方 API 代理
VITE_PROXY_ADDITIONAL_3_TARGET=https://api.third-party.com
VITE_PROXY_ADDITIONAL_3_PREFIX=/external
VITE_PROXY_ADDITIONAL_3_REWRITE_FROM=^/external
VITE_PROXY_ADDITIONAL_3_REWRITE_TO=/v1
VITE_PROXY_ADDITIONAL_3_CHANGE_ORIGIN=true
VITE_PROXY_ADDITIONAL_3_WS=false
```

## 使用方法

### 1. 创建环境文件

根据需要创建对应的环境文件：

- `.env` - 默认配置
- `.env.local` - 本地开发配置（会被 git 忽略）
- `.env.development` - 开发环境配置
- `.env.test` - 测试环境配置
- `.env.staging` - 预发布环境配置
- `.env.production` - 生产环境配置

### 2. 配置环境变量

在对应的环境文件中添加代理配置：

```bash
# 示例：本地开发配置
VITE_PROXY_TARGET=http://127.0.0.1:8000
VITE_PROXY_REWRITE_TO=/apiv2
```

### 3. 启动项目

```bash
# 使用默认配置
npm run dev

# 使用特定环境配置
npm run dev --mode test
npm run dev --mode staging
```

## 配置参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `VITE_PROXY_TARGET` | 代理目标地址（必填） | - | `http://127.0.0.1:8000` |
| `VITE_PROXY_PATH_PREFIX` | 代理路径前缀 | `/api` | `/api` |
| `VITE_PROXY_REWRITE_FROM` | 重写规则：匹配模式 | `^/api` | `^/api` |
| `VITE_PROXY_REWRITE_TO` | 重写规则：替换内容 | `/apiv2` | `/apiv2` |
| `VITE_PROXY_CHANGE_ORIGIN` | 是否改变源地址 | `true` | `true` |
| `VITE_PROXY_WS` | 是否支持 WebSocket | `true` | `true` |

## 调试

启动项目时，控制台会输出代理配置信息：

```
[代理配置] 环境变量配置: {
  target: 'http://127.0.0.1:8000',
  pathPrefix: '/api',
  rewriteFrom: '^/api',
  rewriteTo: '/apiv2',
  changeOrigin: true,
  ws: true
}
[代理配置] 最终配置: {
  '/api': {
    target: 'http://127.0.0.1:8000',
    changeOrigin: true,
    ws: true,
    rewrite: [Function]
  }
}
```

## 注意事项

1. **环境变量优先级**：`.env.local` > `.env.[mode]` > `.env`
2. **安全性**：不要在 `.env` 文件中存储敏感信息
3. **Git 管理**：`.env.local` 会被 git 忽略，其他 `.env.*` 文件会被提交
4. **重启服务**：修改环境变量后需要重启开发服务器
