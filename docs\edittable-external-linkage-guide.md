# EditTable 外部联动功能使用指南

## 功能概述

EditTable 外部联动功能允许外部表单的字段值变化时，动态控制 edittable 内部编辑表单的字段显示/隐藏、必填状态等。这个功能特别适用于复杂的业务场景，比如根据订单类型显示不同的商品字段，或根据客户类型调整必填字段等。

## 配置结构

### 基本配置格式

```typescript
{
  field: 'order_items',
  title: '订单明细',
  type: 'edittable',
  config: {
    columns: [...], // 表格列配置
    form: [...],    // 编辑表单字段配置
    tabList: [...], // 默认数据
    // 外部联动配置
    externalLinkage: {
      triggerFields: ['order_type', 'customer_type'], // 监听的外部字段
      rules: {
        // 字段显示/隐藏规则
        visibility: {
          targetFields: [
            {
              field: 'special_discount', // 目标字段名
              showWhen: [
                {
                  field: 'order_type',
                  operator: '=',
                  value: 'wholesale'
                }
              ]
            },
            {
              field: 'retail_price',
              hideWhen: [
                {
                  field: 'order_type',
                  operator: '=',
                  value: 'wholesale'
                }
              ]
            }
          ]
        },
        // 字段必填规则
        required: {
          targetFields: [
            {
              field: 'tax_number',
              requiredWhen: [
                {
                  field: 'customer_type',
                  operator: '=',
                  value: 'enterprise'
                }
              ]
            }
          ]
        }
      }
    }
  }
}
```

## 支持的操作符

- `=`: 等于
- `!=`: 不等于
- `>`: 大于
- `<`: 小于
- `>=`: 大于等于
- `<=`: 小于等于
- `in`: 包含在数组中
- `not_in`: 不包含在数组中

## 使用示例

### 示例1：根据订单类型控制字段显示

```php
<?php
// PHP 后端配置示例
$orderFormConfig = [
    [
        'field' => 'order_type',
        'title' => '订单类型',
        'type' => 'select',
        'required' => true,
        'config' => [
            'options' => [
                ['label' => '零售订单', 'value' => 'retail'],
                ['label' => '批发订单', 'value' => 'wholesale'],
                ['label' => '企业订单', 'value' => 'enterprise']
            ]
        ]
    ],
    [
        'field' => 'customer_level',
        'title' => '客户等级',
        'type' => 'select',
        'config' => [
            'options' => [
                ['label' => '普通客户', 'value' => 'normal'],
                ['label' => 'VIP客户', 'value' => 'vip'],
                ['label' => '钻石客户', 'value' => 'diamond']
            ]
        ]
    ],
    [
        'field' => 'order_items',
        'title' => '订单明细',
        'type' => 'edittable',
        'config' => [
            'columns' => [
                ['field' => 'product_name', 'title' => '商品名称', 'type' => 'text'],
                ['field' => 'quantity', 'title' => '数量', 'type' => 'number'],
                ['field' => 'retail_price', 'title' => '零售价', 'type' => 'number'],
                ['field' => 'wholesale_price', 'title' => '批发价', 'type' => 'number'],
                ['field' => 'vip_discount', 'title' => 'VIP折扣', 'type' => 'number'],
                ['field' => 'tax_number', 'title' => '税号', 'type' => 'text']
            ],
            'form' => [
                [
                    'field' => 'product_name',
                    'title' => '商品名称',
                    'type' => 'input',
                    'required' => true
                ],
                [
                    'field' => 'quantity',
                    'title' => '数量',
                    'type' => 'inputNumber',
                    'required' => true
                ],
                [
                    'field' => 'retail_price',
                    'title' => '零售价',
                    'type' => 'inputNumber'
                ],
                [
                    'field' => 'wholesale_price',
                    'title' => '批发价',
                    'type' => 'inputNumber'
                ],
                [
                    'field' => 'vip_discount',
                    'title' => 'VIP折扣',
                    'type' => 'inputNumber'
                ],
                [
                    'field' => 'tax_number',
                    'title' => '税号',
                    'type' => 'input'
                ]
            ],
            // 外部联动配置
            'externalLinkage' => [
                'triggerFields' => ['order_type', 'customer_level'],
                'rules' => [
                    'visibility' => [
                        'targetFields' => [
                            // 只有批发订单才显示批发价字段
                            [
                                'field' => 'wholesale_price',
                                'showWhen' => [
                                    [
                                        'field' => 'order_type',
                                        'operator' => '=',
                                        'value' => 'wholesale'
                                    ]
                                ]
                            ],
                            // 零售订单隐藏批发价字段
                            [
                                'field' => 'wholesale_price',
                                'hideWhen' => [
                                    [
                                        'field' => 'order_type',
                                        'operator' => '=',
                                        'value' => 'retail'
                                    ]
                                ]
                            ],
                            // VIP客户才显示VIP折扣字段
                            [
                                'field' => 'vip_discount',
                                'showWhen' => [
                                    [
                                        'field' => 'customer_level',
                                        'operator' => 'in',
                                        'value' => ['vip', 'diamond']
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'required' => [
                        'targetFields' => [
                            // 企业订单必须填写税号
                            [
                                'field' => 'tax_number',
                                'requiredWhen' => [
                                    [
                                        'field' => 'order_type',
                                        'operator' => '=',
                                        'value' => 'enterprise'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ]
];

return $orderFormConfig;
?>
```

### 示例2：复杂的多条件联动

```php
<?php
// 根据产品类型和销售区域控制字段显示
$productFormConfig = [
    [
        'field' => 'product_category',
        'title' => '产品类别',
        'type' => 'select',
        'config' => [
            'options' => [
                ['label' => '电子产品', 'value' => 'electronics'],
                ['label' => '服装', 'value' => 'clothing'],
                ['label' => '食品', 'value' => 'food']
            ]
        ]
    ],
    [
        'field' => 'sales_region',
        'title' => '销售区域',
        'type' => 'select',
        'config' => [
            'options' => [
                ['label' => '国内', 'value' => 'domestic'],
                ['label' => '国外', 'value' => 'international']
            ]
        ]
    ],
    [
        'field' => 'product_details',
        'title' => '产品详情',
        'type' => 'edittable',
        'config' => [
            'columns' => [
                ['field' => 'name', 'title' => '产品名称', 'type' => 'text'],
                ['field' => 'warranty_period', 'title' => '保修期', 'type' => 'number'],
                ['field' => 'size_chart', 'title' => '尺码表', 'type' => 'text'],
                ['field' => 'expiry_date', 'title' => '保质期', 'type' => 'date'],
                ['field' => 'export_license', 'title' => '出口许可证', 'type' => 'text']
            ],
            'form' => [
                ['field' => 'name', 'title' => '产品名称', 'type' => 'input', 'required' => true],
                ['field' => 'warranty_period', 'title' => '保修期(月)', 'type' => 'inputNumber'],
                ['field' => 'size_chart', 'title' => '尺码表', 'type' => 'textarea'],
                ['field' => 'expiry_date', 'title' => '保质期', 'type' => 'datePicker'],
                ['field' => 'export_license', 'title' => '出口许可证号', 'type' => 'input']
            ],
            'externalLinkage' => [
                'triggerFields' => ['product_category', 'sales_region'],
                'rules' => [
                    'visibility' => [
                        'targetFields' => [
                            // 电子产品才显示保修期
                            [
                                'field' => 'warranty_period',
                                'showWhen' => [
                                    [
                                        'field' => 'product_category',
                                        'operator' => '=',
                                        'value' => 'electronics'
                                    ]
                                ]
                            ],
                            // 服装才显示尺码表
                            [
                                'field' => 'size_chart',
                                'showWhen' => [
                                    [
                                        'field' => 'product_category',
                                        'operator' => '=',
                                        'value' => 'clothing'
                                    ]
                                ]
                            ],
                            // 食品才显示保质期
                            [
                                'field' => 'expiry_date',
                                'showWhen' => [
                                    [
                                        'field' => 'product_category',
                                        'operator' => '=',
                                        'value' => 'food'
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'required' => [
                        'targetFields' => [
                            // 国外销售必须填写出口许可证
                            [
                                'field' => 'export_license',
                                'requiredWhen' => [
                                    [
                                        'field' => 'sales_region',
                                        'operator' => '=',
                                        'value' => 'international'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ]
];

return $productFormConfig;
?>
```

## 工作原理

1. **监听外部字段变化**：EditTable 组件通过 Vue 的 `inject` 机制获取外部表单的值
2. **评估联动条件**：当外部字段值发生变化时，根据配置的规则评估每个目标字段的显示/隐藏和必填状态
3. **动态调整 Schema**：根据评估结果动态调整内部编辑表单的 schema 配置
4. **实时更新界面**：表单界面实时反映字段的显示/隐藏和必填状态变化

## 注意事项

1. **性能考虑**：外部联动会在每次外部字段变化时重新计算，建议合理配置 `triggerFields`，只监听必要的字段
2. **字段命名**：确保 `targetFields` 中的 `field` 名称与 edittable 的 `form` 配置中的字段名称完全一致
3. **条件逻辑**：多个条件之间是 AND 关系，即所有条件都满足时才会执行相应的操作
4. **默认状态**：如果没有配置联动规则，字段将保持原始的显示状态和必填状态

## 调试技巧

1. **控制台日志**：开启浏览器控制台，查看 `[EditTable外部联动]` 相关的日志信息
2. **字段检查**：确认外部表单字段名称和 edittable 内部字段名称的一致性
3. **条件测试**：逐步测试每个条件，确保操作符和值的正确性

这个功能为复杂的表单联动提供了强大而灵活的解决方案，可以大大提升用户体验和数据录入的准确性。

## 实际应用场景

### 1. 电商订单管理

- 根据订单类型（零售/批发/企业）显示不同的价格字段
- 根据客户等级显示相应的折扣字段
- 根据销售区域控制税费和运费字段

### 2. 人力资源管理

- 根据员工类型显示不同的薪资结构字段
- 根据部门显示相应的考核指标字段
- 根据职级控制权限相关字段的必填状态

### 3. 项目管理

- 根据项目类型显示不同的里程碑字段
- 根据项目规模控制资源分配字段
- 根据客户类型调整交付标准字段

### 4. 财务管理

- 根据凭证类型显示相应的科目字段
- 根据业务类型控制税务相关字段
- 根据金额范围显示审批流程字段

## 最佳实践

1. **合理设计触发字段**：选择变化频率适中且业务意义明确的字段作为触发字段
2. **避免过度复杂**：联动规则不宜过于复杂，保持逻辑清晰易懂
3. **提供默认值**：为可能被隐藏的字段提供合理的默认值
4. **用户提示**：在界面上适当提示用户字段变化的原因
5. **测试覆盖**：充分测试各种联动场景，确保逻辑正确

## 技术实现细节

### 前端实现

- 使用 Vue 3 的 `provide/inject` 机制实现跨组件通信
- 通过 `watch` 监听外部表单值变化
- 动态调整 Vben Form 的 schema 配置

### 后端配置

- 在 edittable 的 config 中添加 externalLinkage 配置
- 支持多种操作符和复杂的条件组合
- 配置结构清晰，易于维护和扩展

这个功能为复杂的表单联动提供了强大而灵活的解决方案，可以大大提升用户体验和数据录入的准确性。
