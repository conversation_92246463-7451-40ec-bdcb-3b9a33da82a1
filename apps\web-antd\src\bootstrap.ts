import { createApp, watchEffect } from 'vue';

import { registerAccessDirective } from '@vben/access';
import { registerLoadingDirective } from '@vben/common-ui/es/loading';
import { preferences } from '@vben/preferences';
import { initStores, useAccessStore } from '@vben/stores';
import '@vben/styles';
import '@vben/styles/antd';

import { useTitle } from '@vueuse/core';

import { $t, setupI18n } from '#/locales';

import { initComponentAdapter } from './adapter/component';
import { initSetupVbenForm } from './adapter/form';
import { refreshToken } from './api';
import App from './app.vue';
import { registerGlobalComponents } from './components';
import { router } from './router';

async function bootstrap(namespace: string) {
  // 初始化组件适配器
  await initComponentAdapter();

  // 初始化表单组件
  await initSetupVbenForm();

  // // 设置弹窗的默认配置
  // setDefaultModalProps({
  //   fullscreenButton: false,
  // });
  // // 设置抽屉的默认配置
  // setDefaultDrawerProps({
  //   zIndex: 1020,
  // });

  const app = createApp(App);

  // 注册v-loading指令
  registerLoadingDirective(app, {
    loading: 'loading', // 在这里可以自定义指令名称，也可以明确提供false表示不注册这个指令
    spinning: 'spinning',
  });

  // 国际化 i18n 配置
  await setupI18n(app);

  // 配置 pinia-tore
  await initStores(app, { namespace });
  await renewToken();

  // 安装权限指令
  registerAccessDirective(app);

  // 注册全局组件
  registerGlobalComponents(app);

  // 初始化 tippy
  const { initTippy } = await import('@vben/common-ui/es/tippy');
  initTippy(app);

  // 配置路由及路由守卫
  app.use(router);

  // 配置Motion插件
  const { MotionPlugin } = await import('@vben/plugins/motion');
  app.use(MotionPlugin);

  // 动态更新标题
  watchEffect(() => {
    if (preferences.app.dynamicTitle) {
      const routeTitle = router.currentRoute.value.meta?.title;
      const pageTitle =
        (routeTitle ? `${$t(routeTitle)} - ` : '') + preferences.app.name;
      useTitle(pageTitle);
    }
  });

  app.mount('#app');
}

async function renewToken() {
  // 检查URL中是否包含token参数
  if (window.location.href.includes('token=')) {
    const accessStore = useAccessStore();

    try {
      // 提取token参数 - 支持多种格式
      let token = '';

      // 尝试从hash中提取 (#/token=xxx)
      const hashMatch = window.location.hash.match(/token=([^&]*)/);
      if (hashMatch && hashMatch[1]) {
        token = decodeURIComponent(hashMatch[1]);
      } else {
        // 尝试从URL参数中提取 (?token=xxx)
        const urlParams = new URLSearchParams(window.location.search);
        const tokenParam = urlParams.get('token');
        if (tokenParam) {
          token = tokenParam;
        } else {
          // 尝试从完整URL中提取
          const tokenIndex = window.location.href.indexOf('token=');
          if (tokenIndex !== -1) {
            token = window.location.href.slice(tokenIndex + 6);
            // 移除可能的其他参数
            const ampIndex = token.indexOf('&');
            if (ampIndex !== -1) {
              token = token.slice(0, ampIndex);
            }
            const hashIndex = token.indexOf('#');
            if (hashIndex !== -1) {
              token = token.slice(0, hashIndex);
            }
          }
        }
      }

      if (token) {
        console.log('[Auth] 从URL中获取到token，正在设置...');

        // 设置token
        accessStore.setAccessToken(token);

        try {
          // 尝试刷新token获取新的token
          const newToken = await refreshToken();
          if (newToken && newToken.token) {
            accessStore.setAccessToken(newToken.token);
            console.log('[Auth] Token刷新成功');
          }
        } catch (error) {
          console.warn('[Auth] Token刷新失败，使用原token:', error);
          // 如果刷新失败，继续使用原token
        }

        // 获取用户信息以确保认证状态正确
        try {
          const { useAuthStore } = await import('#/store');
          const authStore = useAuthStore();
          await authStore.fetchUserInfo();
          console.log('[Auth] 用户信息获取成功');
        } catch (error) {
          console.warn('[Auth] 用户信息获取失败:', error);
        }

        // 清理URL中的token参数并跳转到首页
        const cleanUrl = window.location.origin + window.location.pathname;
        console.log('[Auth] 跳转到清理后的URL:', cleanUrl);

        // 使用 replace 而不是 href 来避免历史记录问题
        window.location.replace(cleanUrl);
      }
    } catch (error) {
      console.error('[Auth] Token处理失败:', error);
      // 如果处理失败，清理URL
      const cleanUrl = window.location.origin + window.location.pathname;
      window.location.replace(cleanUrl);
    }
  }
}

export { bootstrap };
