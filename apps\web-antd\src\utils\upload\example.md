# Upload 组件值转换使用示例

## 问题说明

Upload 组件的表单值是文件对象数组，包含详细的文件信息：
```javascript
// Upload 组件的原始值
[
  {
    uid: "vc-upload-1754364504817-2",
    name: "image.jpg",
    status: "done",
    url: "path/to/image.jpg",
    response: "path/to/image.jpg"
  }
]
```

但是表单提交时，我们需要的是简单的路径字符串：
```javascript
// 期望的提交值
["path/to/image.jpg"]
// 或单文件时
"path/to/image.jpg"
```

## 解决方案

使用 `processUploadFieldsInFormData` 函数在表单提交时转换值：

### 1. 在 Vue 组件中使用

```vue
<template>
  <div>
    <VbenForm 
      ref="formRef" 
      :schema="formSchema" 
      @submit="handleSubmit" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { VbenForm } from '#/adapter/form';
import { transformBackendSearchToSchema } from '#/utils/search-schema';
import { processUploadFieldsInFormData } from '#/utils/upload/transform';

// 后端字段配置
const backendFields = [
  {
    field: 'avatar',
    title: '头像',
    type: 'Upload',
    config: {
      maxCount: 1,
      multiple: false,
      uploadText: '上传头像',
    },
  },
  {
    field: 'attachments',
    title: '附件',
    type: 'Upload',
    config: {
      uploadText: '上传附件',
    },
  },
];

// 转换为表单 schema
const formSchema = computed(() => {
  return transformBackendSearchToSchema(backendFields);
});

// 处理表单提交
const handleSubmit = (values: any) => {
  console.log('原始表单值:', values);
  
  // 转换 Upload 字段的值
  const processedValues = processUploadFieldsInFormData(values, formSchema.value);
  
  console.log('处理后的表单值:', processedValues);
  
  // 现在 processedValues 中的 Upload 字段是路径字符串
  // avatar: "path/to/avatar.jpg"
  // attachments: ["path/to/file1.jpg", "path/to/file2.jpg"]
  
  // 提交到后端
  submitToBackend(processedValues);
};
</script>
```

### 2. 在通用编辑抽屉中使用

```typescript
// 在 general-editing-drawer 中
import { processUploadFieldsInFormData } from '#/utils/upload/transform';

const handleSubmit = async (values: any) => {
  try {
    // 处理 Upload 字段
    const processedValues = processUploadFieldsInFormData(values, formSchema.value);
    
    // 提交数据
    const result = await submitData(processedValues);
    
    // 处理成功...
  } catch (error) {
    // 处理错误...
  }
};
```

### 3. 手动处理特定字段

```typescript
import { transformUploadValue } from '#/utils/upload/transform';

const handleSubmit = (values: any) => {
  const processedValues = { ...values };
  
  // 手动处理特定的 Upload 字段
  if (processedValues.avatar) {
    processedValues.avatar = transformUploadValue(processedValues.avatar, false); // 单文件
  }
  
  if (processedValues.attachments) {
    processedValues.attachments = transformUploadValue(processedValues.attachments, true); // 多文件
  }
  
  // 提交处理后的数据
  submitToBackend(processedValues);
};
```

## 工具函数说明

### `processUploadFieldsInFormData(formData, schema)`
- 自动检测 schema 中的 Upload 字段
- 根据字段配置自动判断单选/多选模式
- 批量转换所有 Upload 字段的值

### `transformUploadValue(fileList, multiple)`
- 手动转换单个 Upload 字段的值
- `multiple`: true 返回数组，false 返回字符串

### `isUploadField(schema, fieldName)`
- 检测指定字段是否是 Upload 类型

### `getUploadMultiple(schema, fieldName)`
- 获取 Upload 字段的多选配置

## 注意事项

1. **必须在表单提交时转换**：Upload 组件内部需要文件对象来显示文件列表，只有在提交时才转换为路径字符串。

2. **保持组件功能完整**：转换不会影响 Upload 组件的显示、删除、重新上传等功能。

3. **支持单选和多选**：工具函数会根据配置自动判断返回字符串还是数组。

4. **错误处理**：如果文件上传失败或状态不是 'done'，会被自动过滤掉。
