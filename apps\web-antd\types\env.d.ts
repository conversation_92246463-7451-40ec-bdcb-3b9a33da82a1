/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_PROXY_TARGET: string;
  readonly VITE_PROXY_PATH_PREFIX: string;
  readonly VITE_PROXY_REWRITE_FROM: string;
  readonly VITE_PROXY_REWRITE_TO: string;
  readonly VITE_PROXY_CHANGE_ORIGIN: string;
  readonly VITE_PROXY_WS: string;
  readonly VITE_GLOB_API_URL: string;
  readonly VITE_GLOB_OTHER_API_URL: string;
  // 其他环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// 扩展 process.env 类型
declare namespace NodeJS {
  interface ProcessEnv {
    readonly VITE_PROXY_TARGET?: string;
    readonly VITE_PROXY_PATH_PREFIX?: string;
    readonly VITE_PROXY_REWRITE_FROM?: string;
    readonly VITE_PROXY_REWRITE_TO?: string;
    readonly VITE_PROXY_CHANGE_ORIGIN?: string;
    readonly VITE_PROXY_WS?: string;
    readonly VITE_GLOB_API_URL?: string;
    readonly VITE_GLOB_OTHER_API_URL?: string;
  }
}
