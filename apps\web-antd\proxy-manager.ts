/**
 * 代理配置管理器
 * 完全基于环境变量的代理配置
 */
export const ProxyManager = {
  /**
   * 从环境变量获取代理配置
   * @param env 环境变量对象
   * @returns 代理配置对象
   */
  getProxyConfig(env: Record<string, string>) {
    const proxyConfigs: Record<string, any> = {};

    // 获取基础配置
    const target = env.VITE_PROXY_TARGET;
    const pathPrefix = env.VITE_PROXY_PATH_PREFIX || '/api';
    const rewriteFrom = env.VITE_PROXY_REWRITE_FROM || '^/api';
    const rewriteTo = env.VITE_PROXY_REWRITE_TO ?? ''; // 使用 ?? 确保空字符串不被替换
    const changeOrigin = env.VITE_PROXY_CHANGE_ORIGIN !== 'false'; // 默认为 true
    const ws = env.VITE_PROXY_WS !== 'false'; // 默认为 true

    // 如果没有配置目标地址，返回空配置
    if (!target) {
      return {};
    }

    // 基础代理配置
    proxyConfigs[pathPrefix] = {
      target,
      changeOrigin,
      ws,
      rewrite: (path: string) => {
        const regex = new RegExp(rewriteFrom);
        return path.replace(regex, rewriteTo);
      },
    };

    // 支持多个代理配置
    // 格式：VITE_PROXY_ADDITIONAL_1_TARGET=http://localhost:3000
    //      VITE_PROXY_ADDITIONAL_1_PREFIX=/upload
    //      VITE_PROXY_ADDITIONAL_1_REWRITE_FROM=^/upload
    //      VITE_PROXY_ADDITIONAL_1_REWRITE_TO=/files
    let index = 1;
    while (env[`VITE_PROXY_ADDITIONAL_${index}_TARGET`]) {
      const additionalTarget = env[`VITE_PROXY_ADDITIONAL_${index}_TARGET`];
      const additionalPrefix =
        env[`VITE_PROXY_ADDITIONAL_${index}_PREFIX`] || `/api${index}`;
      const additionalRewriteFrom =
        env[`VITE_PROXY_ADDITIONAL_${index}_REWRITE_FROM`] || `^/api${index}`;
      const additionalRewriteTo =
        env[`VITE_PROXY_ADDITIONAL_${index}_REWRITE_TO`] || '';
      const additionalChangeOrigin =
        env[`VITE_PROXY_ADDITIONAL_${index}_CHANGE_ORIGIN`] !== 'false';
      const additionalWs = env[`VITE_PROXY_ADDITIONAL_${index}_WS`] !== 'false';

      proxyConfigs[additionalPrefix] = {
        target: additionalTarget,
        changeOrigin: additionalChangeOrigin,
        ws: additionalWs,
        rewrite: (path: string) => {
          const regex = new RegExp(additionalRewriteFrom);
          return path.replace(regex, additionalRewriteTo);
        },
      };

      console.log(`[代理配置] 添加额外代理 ${index}:`, {
        prefix: additionalPrefix,
        target: additionalTarget,
        rewriteFrom: additionalRewriteFrom,
        rewriteTo: additionalRewriteTo,
      });

      index++;
    }

    console.log(`[代理配置] 最终配置:`, proxyConfigs);
    return proxyConfigs;
  },

  /**
   * 获取环境配置预设
   * @param envName 环境名称
   * @returns 环境配置对象
   */
  getEnvPreset(envName: string): Record<string, string> {
    const presets: Record<string, Record<string, string>> = {
      // 本地开发环境 - 需要重写路径到 /apiv2
      local: {
        VITE_PROXY_TARGET: 'http://127.0.0.1:8000',
        VITE_PROXY_REWRITE_FROM: '^/api',
        VITE_PROXY_REWRITE_TO: '/apiv2',
      },
      // 远程环境（测试/正式）- 直接使用 /api 路径
      remote: {
        VITE_PROXY_TARGET: 'https://erp.gbuilderchina.com/testapiv2/',
        VITE_PROXY_REWRITE_FROM: '^/api',
        VITE_PROXY_REWRITE_TO: '',
      },
    };

    return presets[envName] || presets.local;
  },

  /**
   * 获取所有可用的环境预设
   */
  getAvailablePresets(): string[] {
    return ['local', 'remote'];
  },

  /**
   * 验证代理配置
   * @param config 代理配置对象
   * @returns 验证结果
   */
  validateConfig(config: Record<string, any>): {
    errors: string[];
    valid: boolean;
  } {
    const errors: string[] = [];

    // 检查是否有代理配置
    if (Object.keys(config).length === 0) {
      errors.push('未找到任何代理配置');
    }

    // 检查每个代理配置
    Object.entries(config).forEach(([path, proxyConfig]: [string, any]) => {
      if (!proxyConfig.target) {
        errors.push(`代理路径 "${path}" 缺少 target 配置`);
      }

      if (typeof proxyConfig.rewrite !== 'function') {
        errors.push(`代理路径 "${path}" 的 rewrite 配置无效`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  },
};

export default ProxyManager;
