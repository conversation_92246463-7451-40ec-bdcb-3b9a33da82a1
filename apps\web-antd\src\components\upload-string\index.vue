<template>
  <Upload v-bind="$attrs" :file-list="fileList" @change="handleChange">
    <slot />
  </Upload>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { Upload } from 'ant-design-vue';
import {
  stringToFileList,
  stringToFileObject,
  fileListToString,
} from '#/utils/upload/stringToFileObject';

interface Props {
  value?: string;
  multiple?: boolean;
}

interface Emits {
  (e: 'update:value', value: string): void;
  (e: 'change', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  multiple: false,
});

const emit = defineEmits<Emits>();

// 将字符串值转换为文件列表
const fileList = computed(() => {
  if (!props.value) {
    return [];
  }

  if (props.multiple) {
    // 多文件：将逗号分隔的字符串转换为文件对象数组
    return stringToFileList(props.value);
  } else {
    // 单文件：将字符串转换为单个文件对象
    const fileObj = stringToFileObject(props.value);
    return fileObj ? [fileObj] : [];
  }
});

// 处理文件列表变化
function handleChange(info: any) {
  const { fileList } = info;

  // 将文件列表转换为字符串值
  const stringValue = fileListToString(fileList, props.multiple);

  // 发出更新事件
  emit('update:value', stringValue);
  emit('change', stringValue);
}

// 监听 value 变化
watch(
  () => props.value,
  (newValue) => {
    // 值变化时的处理逻辑
  },
);
</script>
