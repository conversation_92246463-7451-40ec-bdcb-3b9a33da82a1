<template>
  <Upload v-bind="$attrs" :file-list="fileList" @change="handleChange">
    <slot />
  </Upload>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Upload } from 'ant-design-vue';
import {
  stringToFileList,
  stringToFileObject,
  fileListToString,
} from '#/utils/upload/stringToFileObject';

interface Props {
  value?: string;
  multiple?: boolean;
}

interface Emits {
  (e: 'update:value', value: string): void;
  (e: 'change', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  multiple: false,
});

const emit = defineEmits<Emits>();

// 内部文件列表状态
const internalFileList = ref<any[]>([]);

// 计算显示的文件列表
const fileList = computed(() => {
  // 如果有外部传入的值，优先使用外部值
  if (props.value) {
    if (props.multiple) {
      return stringToFileList(props.value);
    } else {
      const fileObj = stringToFileObject(props.value);
      return fileObj ? [fileObj] : [];
    }
  }

  // 否则使用内部状态
  return internalFileList.value;
});

// 处理文件列表变化
function handleChange(info: any) {
  const { fileList: newFileList } = info;

  // 调试信息
  console.warn('[UploadString Debug] 文件列表变化:', {
    fileCount: newFileList.length,
    files: newFileList.map((file: any) => ({
      name: file.name,
      status: file.status,
      response: file.response,
      url: file.url,
    })),
  });

  // 更新内部状态
  internalFileList.value = [...newFileList];

  // 将文件列表转换为字符串值
  const stringValue = fileListToString(newFileList, props.multiple);

  console.warn('[UploadString Debug] 转换后的字符串值:', stringValue);

  // 只有当字符串值不为空时才发出更新事件
  if (stringValue) {
    emit('update:value', stringValue);
    emit('change', stringValue);
  } else {
    // 如果字符串值为空（比如删除文件），也要通知外部
    emit('update:value', '');
    emit('change', '');
  }
}

// 监听外部 value 变化，同步内部状态
watch(
  () => props.value,
  (newValue) => {
    if (!newValue) {
      // 如果外部值为空，清空内部状态
      internalFileList.value = [];
    }
  },
  { immediate: true },
);
</script>
