# Upload 组件字符串值处理

## 📝 功能说明

现在 Upload 组件支持直接使用字符串作为表单字段值，而不需要复杂的文件对象数组。

## 🎯 主要特性

### ✅ **字符串值绑定**
- 表单字段值直接是字符串（单文件）或逗号分隔的字符串（多文件）
- 上传成功后，接口返回的字符串直接作为字段值
- 编辑模式下，字符串值自动转换为文件对象供组件显示

### ✅ **自动转换**
- **上传时**：文件对象 → 字符串值
- **回显时**：字符串值 → 文件对象
- **提交时**：直接使用字符串值

## 🔧 配置示例

### 单文件上传

```php
// PHP 后端配置
$uploadField = [
    'field' => 'avatar',
    'title' => '头像',
    'type' => 'Upload',
    'config' => [
        'accept' => '.jpg,.jpeg,.png',
        'maxCount' => 1,
        'multiple' => false,
        'listType' => 'picture-card',
    ],
];
```

**表单值示例**：
```javascript
{
  avatar: "https://example.com/uploads/avatar.jpg"
}
```

### 多文件上传

```php
// PHP 后端配置
$uploadField = [
    'field' => 'documents',
    'title' => '文档',
    'type' => 'Upload',
    'config' => [
        'accept' => '.pdf,.doc,.docx',
        'maxCount' => 5,
        'multiple' => true,
        'listType' => 'text',
    ],
];
```

**表单值示例**：
```javascript
{
  documents: "https://example.com/doc1.pdf,https://example.com/doc2.pdf"
}
```

## 🔄 数据流程

### 1. **上传流程**
```
用户选择文件 → 调用上传接口 → 接口返回字符串 → 设置为表单字段值
```

### 2. **编辑回显流程**
```
字符串值 → 转换为文件对象 → Upload 组件显示 → 用户可继续操作
```

### 3. **表单提交流程**
```
表单提交 → 直接使用字符串值 → 后端接收字符串
```

## 🛠️ 技术实现

### 核心工具函数

- `stringToFileObject()` - 字符串转文件对象
- `stringToFileList()` - 多个字符串转文件对象数组
- `fileListToString()` - 文件对象数组转字符串
- `createStringValueHandler()` - 创建字符串值处理器

### 自动处理机制

1. **上传成功时**：自动提取接口返回的字符串作为字段值
2. **编辑模式时**：自动将字符串值转换为文件对象供组件显示
3. **表单绑定**：使用 `v-model:value` 而不是 `v-model:fileList`

## 📋 接口要求

### 上传接口返回格式

**推荐格式**（直接返回字符串）：
```json
"https://example.com/uploads/file.jpg"
```

**也支持对象格式**：
```json
{
  "data": "https://example.com/uploads/file.jpg",
  "url": "https://example.com/uploads/file.jpg"
}
```

## 🎨 UI 显示

- **单文件**：显示为图片预览或文件名
- **多文件**：显示为文件列表
- **编辑模式**：自动回显已上传的文件
- **删除操作**：从字符串中移除对应的 URL

## 🔍 调试信息

在开发模式下，可以在浏览器控制台查看：
- 上传成功的响应数据
- 字符串值转换过程
- 表单字段值变化

## 💡 使用建议

1. **单文件上传**：推荐使用 `maxCount: 1, multiple: false`
2. **多文件上传**：使用逗号分隔的字符串格式
3. **图片上传**：使用 `listType: 'picture-card'` 获得更好的预览效果
4. **文档上传**：使用 `listType: 'text'` 显示文件名列表
