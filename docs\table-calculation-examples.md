# 表格数据计算配置示例

## 概述

表格数据计算功能允许在表单中自动计算表格某一列或多列的汇总值。支持多种计算类型：求和、平均值、计数、最大值、最小值等。

## 基础配置结构

```json
{
  "field": "计算结果字段名",
  "title": "字段标题",
  "type": "number",
  "config": {
    "readonly": true,
    "prefix": "¥",
    "calculation": {
      "type": "sum",
      "tableFields": ["amount"],
      "triggerFields": ["order_items"],
      "precision": 2,
      "defaultValue": 0
    }
  }
}
```

## 配置参数说明

- **type**: 计算类型 (`sum`, `average`, `count`, `max`, `min`, `product`, `subtract`, `divide`, `formula`, `custom`)
- **tableFields**: 参与计算的表格字段列表
- **triggerFields**: 触发计算的字段列表（通常是包含表格数据的字段）
- **precision**: 计算结果的小数位数
- **defaultValue**: 默认值（当无数据时使用）
- **filter**: 过滤条件（可选，用于条件计算）

## 计算类型示例

### 1. 求和计算 (sum)

计算表格中某列的总和：

```json
{
  "field": "total_amount",
  "title": "总金额",
  "type": "number",
  "config": {
    "readonly": true,
    "prefix": "¥",
    "calculation": {
      "type": "sum",
      "tableFields": ["amount"],
      "triggerFields": ["order_items"],
      "precision": 2,
      "defaultValue": 0
    }
  }
}
```

### 2. 计数 (count)

计算表格的行数：

```json
{
  "field": "item_count",
  "title": "商品数量",
  "type": "number",
  "config": {
    "readonly": true,
    "suffix": "件",
    "calculation": {
      "type": "count",
      "tableFields": ["product_id"],
      "triggerFields": ["order_items"],
      "precision": 0,
      "defaultValue": 0
    }
  }
}
```

### 3. 平均值 (average)

计算表格中某列的平均值：

```json
{
  "field": "average_price",
  "title": "平均单价",
  "type": "number",
  "config": {
    "readonly": true,
    "prefix": "¥",
    "calculation": {
      "type": "average",
      "tableFields": ["unit_price"],
      "triggerFields": ["order_items"],
      "precision": 2,
      "defaultValue": 0
    }
  }
}
```

### 4. 最大值 (max)

计算表格中某列的最大值：

```json
{
  "field": "max_price",
  "title": "最高单价",
  "type": "number",
  "config": {
    "readonly": true,
    "prefix": "¥",
    "calculation": {
      "type": "max",
      "tableFields": ["unit_price"],
      "triggerFields": ["order_items"],
      "precision": 2,
      "defaultValue": 0
    }
  }
}
```

### 5. 最小值 (min)

计算表格中某列的最小值：

```json
{
  "field": "min_price",
  "title": "最低单价",
  "type": "number",
  "config": {
    "readonly": true,
    "prefix": "¥",
    "calculation": {
      "type": "min",
      "tableFields": ["unit_price"],
      "triggerFields": ["order_items"],
      "precision": 2,
      "defaultValue": 0
    }
  }
}
```

## 条件计算示例

使用过滤条件计算特定条件下的数据：

```json
{
  "field": "electronics_total",
  "title": "电子产品总金额",
  "type": "number",
  "config": {
    "readonly": true,
    "prefix": "¥",
    "calculation": {
      "type": "sum",
      "tableFields": ["amount"],
      "triggerFields": ["order_items"],
      "filter": [
        {
          "field": "category",
          "operator": "=",
          "value": "electronics"
        }
      ],
      "precision": 2,
      "defaultValue": 0
    }
  }
}
```

## 支持的过滤操作符

- `=` 或 `==`: 等于
- `!=`: 不等于
- `>`: 大于
- `>=`: 大于等于
- `<`: 小于
- `<=`: 小于等于
- `in`: 包含在数组中
- `not_in`: 不包含在数组中
- `like`: 包含字符串
- `not_like`: 不包含字符串

## 多字段计算示例

计算多个字段的总和：

```json
{
  "field": "total_cost",
  "title": "总成本",
  "type": "number",
  "config": {
    "readonly": true,
    "prefix": "¥",
    "calculation": {
      "type": "sum",
      "tableFields": ["material_cost", "labor_cost", "overhead_cost"],
      "triggerFields": ["cost_items"],
      "precision": 2,
      "defaultValue": 0
    }
  }
}
```

## 注意事项

1. **triggerFields** 必须包含表格数据字段，当该字段变化时会触发重新计算
2. **tableFields** 指定要参与计算的表格列名
3. 计算结果字段通常设置为 `readonly: true`
4. 使用 `precision` 控制小数位数，避免浮点数精度问题
5. 设置合适的 `defaultValue`，当表格为空时显示默认值
