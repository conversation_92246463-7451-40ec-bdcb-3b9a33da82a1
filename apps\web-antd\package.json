{"name": "@vben/web-antd", "version": "5.5.7", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-antd"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build:prod": "pnpm vite build --mode production", "build:test": "pnpm vite build --mode test", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "dev:local": "node scripts/switch-proxy.js local && pnpm dev", "dev:remote": "node scripts/switch-proxy.js remote && pnpm dev", "preview": "vite preview", "proxy:switch": "node scripts/switch-proxy.js", "proxy:local": "node scripts/switch-proxy.js local", "proxy:remote": "node scripts/switch-proxy.js remote", "backend:switch": "node switch-backend.js", "backend:local": "node switch-backend.js local", "backend:remote": "node switch-backend.js remote", "dev:backend": "node switch-backend.js local && pnpm dev", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@tanstack/vue-query": "catalog:", "@types/xlsx": "^0.0.36", "@vben-core/menu-ui": "workspace:*", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "lodash": "^4.17.21", "pinia": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "xlsx": "^0.18.5"}, "devDependencies": {"less": "^4.4.0"}}