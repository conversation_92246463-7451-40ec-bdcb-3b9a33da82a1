/**
 * 联动规则转换器
 * 将后端联动配置转换为 Vben 表单的 dependencies 配置
 */

import type {
  BaseCondition,
  ComponentPropsRule,
  DisabledRule,
  DisplayRule,
  LinkageCalculationRule,
  LinkageConfig,
  OptionsRule,
  RequiredRule,
  VisibilityRule,
} from './types';

// 防抖计算的存储对象
const calculationDebounceMap = new Map<string, NodeJS.Timeout>();

// 计算队列，用于处理快速连续的计算请求
const calculationQueue = new Map<
  string,
  {
    values: Record<string, any>;
    formApi: any;
    calculationRule: LinkageCalculationRule;
    timestamp: number;
  }
>();

/**
 * 联动规则转换器
 */
export const LinkageRuleConverter = {
  /**
   * 转换联动配置为 Vben 表单依赖规则
   * @param linkage 后端联动配置
   * @returns Vben 表单 dependencies 配置
   */
  convertLinkageToVbenDependencies(linkage: LinkageConfig) {
    if (!linkage || !linkage.rules) {
      return undefined;
    }

    const dependencies: any = {
      triggerFields: linkage.triggerFields || [],
      // 联动触发时清空字段值（但要区分初始化和用户操作）
      trigger: (values: Record<string, any>, form?: any) => {
        // 直接模仿您的工作代码
        if (form && form.setFieldValue && form.fieldName) {
          try {
            form.setFieldValue(form.fieldName, undefined);
          } catch {
            // 静默处理错误
          }
        }
      },
    };

    // 转换显示/隐藏规则（DOM 级别）
    if (linkage.rules.visibility) {
      const visibilityRule = linkage.rules.visibility;
      dependencies.if = (values: Record<string, any>) => {
        return this.evaluateVisibilityRules(visibilityRule, values);
      };
    }

    // 转换显示/隐藏规则（CSS 级别）
    if (linkage.rules.display) {
      const displayRule = linkage.rules.display;
      dependencies.show = (values: Record<string, any>) => {
        return this.evaluateDisplayRules(displayRule, values);
      };
    }

    // 转换禁用规则
    if (linkage.rules.disabled) {
      const disabledRule = linkage.rules.disabled;
      dependencies.disabled = (values: Record<string, any>) => {
        return this.evaluateDisabledRules(disabledRule, values);
      };
    }

    // 转换必填规则
    if (linkage.rules.required) {
      const requiredRule = linkage.rules.required;
      dependencies.required = (values: Record<string, any>) => {
        return this.evaluateRequiredRules(requiredRule, values);
      };
    }

    // 转换动态组件属性
    if (linkage.rules.options || linkage.rules.componentProps) {
      dependencies.componentProps = (
        values: Record<string, any>,
        formApi?: any,
      ) => {
        const props: any = {};

        // 处理动态选项
        if (linkage.rules.options) {
          const newOptions = this.evaluateOptionsRules(
            linkage.rules.options,
            values,
          );
          props.options = newOptions;

          // 方案2: 在 componentProps 中添加清空标记
          if (formApi?.fieldName) {
            const currentValue = formApi.getFieldValue?.(formApi.fieldName);
            if (
              currentValue !== undefined &&
              currentValue !== null &&
              newOptions.length > 0
            ) {
              const validValues = new Set(newOptions.map((opt) => opt.value));
              const isValid = Array.isArray(currentValue)
                ? currentValue.every((val) => validValues.has(val))
                : validValues.has(currentValue);

              if (!isValid) {
                props._shouldClear = true;
                props._clearValue = Array.isArray(currentValue)
                  ? []
                  : undefined;
              }
            }
          }

          // 检查当前字段值是否在新选项中，如果不在则清空
          if (formApi && newOptions.length > 0) {
            this.clearInvalidFieldValue(formApi, newOptions);
          }
        }

        // 处理动态组件属性
        if (linkage.rules.componentProps) {
          const dynamicProps = LinkageRuleConverter.evaluateComponentPropsRules(
            linkage.rules.componentProps,
            values,
          );
          Object.assign(props, dynamicProps);
        }

        return props;
      };
    }

    // 转换计算规则
    if (linkage.rules.calculation) {
      // 增强 trigger 函数以支持计算
      const originalTrigger = dependencies.trigger;
      dependencies.trigger = (values: Record<string, any>, formApi?: any) => {
        // 执行原有的 trigger 逻辑
        if (originalTrigger) {
          originalTrigger(values, formApi);
        }

        // 执行计算逻辑
        if (formApi?.fieldName) {
          this.executeCalculation(linkage.rules.calculation!, values, formApi);
        }
      };

      // 如果没有其他 trigger，创建一个专门用于计算的 trigger
      if (!dependencies.trigger) {
        dependencies.trigger = (values: Record<string, any>, formApi?: any) => {
          if (formApi?.fieldName) {
            this.executeCalculation(
              linkage.rules.calculation!,
              values,
              formApi,
            );
          }
        };
      }
    }

    return dependencies;
  },

  /**
   * 构建映射键
   * @param values 表单值
   * @returns 映射键数组
   */
  buildMappingKeys(values: Record<string, any>): string[] {
    const keys: string[] = [];

    // 单字段映射：field_value
    Object.entries(values).forEach(([field, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        keys.push(`${field}_${value}`);
      }
    });

    // 多字段组合映射：field1_value1_field2_value2
    const fieldEntries = Object.entries(values).filter(
      ([, value]) => value !== undefined && value !== null && value !== '',
    );
    if (fieldEntries.length > 1) {
      const combinedKey = fieldEntries
        .map(([field, value]) => `${field}_${value}`)
        .join('_');
      keys.push(combinedKey);
    }

    // 通配符映射：field_*
    Object.keys(values).forEach((field) => {
      keys.push(`${field}_*`);
    });

    return keys;
  },

  /**
   * 清空无效的字段值
   * @param formApi 表单 API
   * @param validOptions 有效选项列表
   */
  clearInvalidFieldValue(
    formApi: any,
    validOptions: Array<{ label: string; value: any }>,
  ): void {
    try {
      // 直接执行清空逻辑，不再需要初始化检查

      // 获取当前字段名（从 formApi 中推断）
      const fieldName = LinkageRuleConverter.getCurrentFieldName(formApi);
      if (!fieldName) return;

      // 获取当前字段值
      const currentValue = formApi.getFieldValue?.(fieldName);
      if (currentValue === undefined || currentValue === null) return;

      // 检查当前值是否在有效选项中
      const validValues = new Set(validOptions.map((option) => option.value));
      const isValidValue = Array.isArray(currentValue)
        ? currentValue.every((val) => validValues.has(val))
        : validValues.has(currentValue);

      // 如果当前值无效，则清空
      if (!isValidValue) {
        formApi.setFieldValue?.(
          fieldName,
          Array.isArray(currentValue) ? [] : undefined,
        );
      }
    } catch {
      // 静默处理错误
    }
  },

  /**
   * 评估组件属性规则
   * @param rules 组件属性规则
   * @param values 表单值
   * @returns 组件属性对象
   */
  evaluateComponentPropsRules(
    rules: ComponentPropsRule,
    values: Record<string, any>,
  ): Record<string, any> {
    if (!rules.mapping) {
      return rules.default || {};
    }

    // 构建映射键
    const mappingKeys = LinkageRuleConverter.buildMappingKeys(values);

    // 查找匹配的属性
    for (const key of mappingKeys) {
      if (rules.mapping[key]) {
        return { ...rules.default, ...rules.mapping[key] };
      }
    }

    return rules.default || {};
  },

  /**
   * 评估单个条件
   * @param condition 条件配置
   * @param values 表单值
   * @returns 条件是否满足
   */
  evaluateCondition(
    condition: BaseCondition,
    values: Record<string, any>,
  ): boolean {
    const fieldValue = values[condition.field];

    switch (condition.operator) {
      case 'contains': {
        return String(fieldValue).includes(String(condition.value));
      }
      case 'custom': {
        // 支持自定义条件函数
        return this.evaluateCustomCondition(condition, values);
      }
      case 'ends_with': {
        return String(fieldValue).endsWith(String(condition.value));
      }
      case 'equals': {
        return fieldValue === condition.value;
      }
      case 'greater': {
        return Number(fieldValue) > Number(condition.value);
      }
      case 'greater_equal': {
        return Number(fieldValue) >= Number(condition.value);
      }
      case 'in': {
        return (
          Array.isArray(condition.value) && condition.value.includes(fieldValue)
        );
      }
      case 'is_empty': {
        return (
          !fieldValue ||
          fieldValue === '' ||
          (Array.isArray(fieldValue) && fieldValue.length === 0)
        );
      }
      case 'is_not_empty': {
        return (
          fieldValue &&
          fieldValue !== '' &&
          (!Array.isArray(fieldValue) || fieldValue.length > 0)
        );
      }
      case 'is_required': {
        // 新增操作符：直接判断是否必填
        // 这个操作符总是返回 true，实际的必填状态由 condition.value 决定
        return true;
      }
      case 'less': {
        return Number(fieldValue) < Number(condition.value);
      }
      case 'less_equal': {
        return Number(fieldValue) <= Number(condition.value);
      }
      case 'not_contains': {
        return !String(fieldValue).includes(String(condition.value));
      }
      case 'not_equals': {
        return fieldValue !== condition.value;
      }
      case 'not_in': {
        return (
          Array.isArray(condition.value) &&
          !condition.value.includes(fieldValue)
        );
      }
      case 'not_regex': {
        return !new RegExp(condition.value).test(String(fieldValue));
      }
      case 'regex': {
        return new RegExp(condition.value).test(String(fieldValue));
      }
      case 'starts_with': {
        return String(fieldValue).startsWith(String(condition.value));
      }
      default: {
        return false;
      }
    }
  },

  /**
   * 评估自定义条件
   * @param condition 条件配置
   * @param values 表单值
   * @returns 条件是否满足
   */
  evaluateCustomCondition(
    condition: BaseCondition,
    values: Record<string, any>,
  ): boolean {
    // 可以通过配置或注册的方式支持自定义条件
    const customConditions = LinkageRuleConverter.getCustomConditions();
    const conditionFn = customConditions[condition.value];

    return conditionFn ? conditionFn(values, condition) : false;
  },

  /**
   * 评估禁用规则
   * @param rules 禁用规则
   * @param values 表单值
   * @returns 是否禁用
   */
  evaluateDisabledRules(
    rules: DisabledRule,
    values: Record<string, any>,
  ): boolean {
    // 如果有 disableWhen 规则，满足任一条件就禁用
    if (rules.disableWhen && rules.disableWhen.length > 0) {
      const shouldDisable = rules.disableWhen.some((condition) =>
        LinkageRuleConverter.evaluateCondition(condition, values),
      );
      if (shouldDisable) return true;
    }

    // 如果有 enableWhen 规则，必须满足其中一个条件才启用
    if (rules.enableWhen && rules.enableWhen.length > 0) {
      const shouldEnable = rules.enableWhen.some((condition) =>
        LinkageRuleConverter.evaluateCondition(condition, values),
      );
      return !shouldEnable;
    }

    return false;
  },

  /**
   * 评估显示规则（CSS 级别）
   * @param rules 显示规则
   * @param values 表单值
   * @returns 是否显示
   */
  evaluateDisplayRules(
    rules: DisplayRule,
    values: Record<string, any>,
  ): boolean {
    return LinkageRuleConverter.evaluateVisibilityRules(rules, values);
  },

  /**
   * 评估选项规则
   * @param rules 选项规则
   * @param values 表单值
   * @returns 选项数组
   */
  evaluateOptionsRules(
    rules: OptionsRule,
    values: Record<string, any>,
  ): Array<{ label: string; value: any }> {
    if (!rules.mapping) {
      return rules.default || [];
    }

    // 优先查找直接值映射（最常用的情况）
    // 例如：values.type = "2" 对应 mapping["2"]
    for (const [_field, value] of Object.entries(values)) {
      if (value !== undefined && value !== null && value !== '') {
        const key = String(value);
        if (rules.mapping[key]) {
          return rules.mapping[key];
        }
      }
    }

    // 备用：构建映射键 - 支持多种映射策略
    const mappingKeys = LinkageRuleConverter.buildMappingKeys(values);

    // 查找匹配的选项
    for (const key of mappingKeys) {
      if (rules.mapping[key]) {
        return rules.mapping[key];
      }
    }

    return rules.default || [];
  },

  /**
   * 评估必填规则
   * @param rules 必填规则
   * @param values 表单值
   * @returns 是否必填
   */
  evaluateRequiredRules(
    rules: RequiredRule,
    values: Record<string, any>,
  ): boolean {
    // 如果有 requiredWhen 规则，满足任一条件就必填
    if (rules.requiredWhen && rules.requiredWhen.length > 0) {
      for (const condition of rules.requiredWhen) {
        if (LinkageRuleConverter.evaluateCondition(condition, values)) {
          // 如果条件的 value 是布尔值，直接返回该值作为必填状态
          if (typeof condition.value === 'boolean') {
            return condition.value;
          }
          // 否则返回 true（传统行为：满足条件就必填）
          return true;
        }
      }
    }

    // 如果有 optionalWhen 规则，满足任一条件就可选
    if (rules.optionalWhen && rules.optionalWhen.length > 0) {
      for (const condition of rules.optionalWhen) {
        if (LinkageRuleConverter.evaluateCondition(condition, values)) {
          // 如果条件的 value 是布尔值，返回相反值
          if (typeof condition.value === 'boolean') {
            return !condition.value;
          }
          // 否则返回 false（传统行为：满足条件就可选）
          return false;
        }
      }
    }

    return false;
  },

  /**
   * 评估显示规则（DOM 级别）
   * @param rules 显示规则
   * @param values 表单值
   * @returns 是否显示
   */
  evaluateVisibilityRules(
    rules: VisibilityRule,
    values: Record<string, any>,
  ): boolean {
    // 如果有 showWhen 规则，必须满足其中一个条件才显示
    if (rules.showWhen && rules.showWhen.length > 0) {
      const shouldShow = rules.showWhen.some((condition) =>
        LinkageRuleConverter.evaluateCondition(condition, values),
      );
      if (!shouldShow) return false;
    }

    // 如果有 hideWhen 规则，满足任一条件就隐藏
    if (rules.hideWhen && rules.hideWhen.length > 0) {
      const shouldHide = rules.hideWhen.some((condition) =>
        LinkageRuleConverter.evaluateCondition(condition, values),
      );
      if (shouldHide) return false;
    }

    return true;
  },

  /**
   * 获取当前字段名
   * @param formApi 表单 API
   * @returns 字段名
   */
  getCurrentFieldName(formApi: any): null | string {
    try {
      // 尝试从 formApi 中获取字段名
      // 这个方法可能需要根据实际的 formApi 结构调整
      if (formApi.fieldName) {
        return formApi.fieldName;
      }

      // 如果无法直接获取，返回 null
      // 在实际使用中，可能需要通过其他方式传递字段名
      return null;
    } catch {
      return null;
    }
  },

  /**
   * 获取自定义条件函数
   * @returns 自定义条件函数映射
   */
  getCustomConditions(): Record<string, (...args: any[]) => any> {
    // 这里可以扩展自定义条件
    return {
      // 示例：检查是否为工作日
      isWorkday: (values: Record<string, any>) => {
        const date = new Date(values.date);
        const day = date.getDay();
        return day >= 1 && day <= 5;
      },
      // 示例：检查年龄范围
      ageInRange: (values: Record<string, any>, condition: BaseCondition) => {
        const age = Number(values.age);
        const [min, max] = condition.value;
        return age >= min && age <= max;
      },
    };
  },

  /**
   * 执行联动计算（带防抖机制）
   * @param calculationRule 计算规则
   * @param values 表单值
   * @param formApi 表单API
   */
  executeCalculation(
    calculationRule: LinkageCalculationRule,
    values: Record<string, any>,
    formApi: any,
  ): void {
    try {
      const fieldName = formApi.fieldName;
      if (!fieldName) {
        console.warn('[联动计算] 缺少字段名，跳过计算');
        return;
      }

      // 防抖处理 - 清除之前的计算定时器
      const debounceKey = `debounce_${fieldName}`;
      if (calculationDebounceMap.has(debounceKey)) {
        clearTimeout(calculationDebounceMap.get(debounceKey)!);
      }

      // 将当前计算请求加入队列
      calculationQueue.set(fieldName, {
        values: { ...values }, // 深拷贝避免引用问题
        formApi,
        calculationRule,
        timestamp: Date.now(),
      });

      // 设置防抖延迟执行
      const debounceTimeout = setTimeout(() => {
        this.executeCalculationImmediate(fieldName);
        calculationDebounceMap.delete(debounceKey);
      }, 150); // 150ms 防抖延迟

      calculationDebounceMap.set(debounceKey, debounceTimeout);
    } catch (error) {
      console.warn('[联动计算] 计算调度失败:', error);
      if (calculationRule.defaultValue !== undefined) {
        formApi.setFieldValue(formApi.fieldName, calculationRule.defaultValue);
      }
    }
  },

  /**
   * 立即执行计算（内部方法）
   * @param fieldName 字段名
   */
  executeCalculationImmediate(fieldName: string): void {
    const queueItem = calculationQueue.get(fieldName);
    if (!queueItem) {
      return;
    }

    const { values, formApi, calculationRule } = queueItem;

    try {
      // 防止递归计算 - 检查是否正在计算中
      const calculationKey = `calculating_${fieldName}`;
      if ((formApi as any)[calculationKey]) {
        console.log(`[联动计算] 字段 ${fieldName} 正在计算中，跳过重复计算`);
        return;
      }

      // 设置计算锁
      (formApi as any)[calculationKey] = true;

      try {
        // 检查条件计算
        if (
          calculationRule.conditions &&
          calculationRule.conditions.length > 0
        ) {
          for (const conditionCalc of calculationRule.conditions) {
            if (this.evaluateCondition(conditionCalc.condition, values)) {
              // 使用满足条件的计算配置
              this.performCalculation(
                conditionCalc.calculation,
                values,
                formApi,
              );
              return;
            }
          }
          // 如果没有条件满足，使用默认值
          if (calculationRule.defaultValue !== undefined) {
            formApi.setFieldValue(fieldName, calculationRule.defaultValue);
          }
          return;
        }

        // 执行普通计算
        this.performCalculation(calculationRule, values, formApi);
      } finally {
        // 清除计算锁和队列项
        delete (formApi as any)[calculationKey];
        calculationQueue.delete(fieldName);
      }
    } catch (error) {
      console.warn('[联动计算] 计算执行失败:', error);
      if (calculationRule.defaultValue !== undefined) {
        formApi.setFieldValue(fieldName, calculationRule.defaultValue);
      }
      // 清理
      calculationQueue.delete(fieldName);
    }
  },

  /**
   * 执行具体的计算逻辑
   * @param calculationRule 计算规则
   * @param values 表单值
   * @param formApi 表单API
   */
  performCalculation(
    calculationRule: LinkageCalculationRule,
    values: Record<string, any>,
    formApi: any,
  ): void {
    const {
      type,
      sourceFields = [],
      tableFields = [],
      triggerFields = [],
      precision = 2,
      defaultValue = 0,
      filter = [],
    } = calculationRule;

    // 获取参与计算的值
    const calculationValues: number[] = [];

    // 从表单字段获取值
    sourceFields.forEach((fieldName) => {
      const value = this.getNestedValue(values, fieldName);
      const numValue = this.parseNumber(value);
      if (!isNaN(numValue) && isFinite(numValue)) {
        calculationValues.push(numValue);
      }
    });

    // 从表格字段获取汇总值
    if (tableFields.length > 0 && triggerFields.length > 0) {
      // 获取表格数据 - 从触发字段中获取表格数据
      const tableData = this.getTableDataFromTriggerFields(
        values,
        triggerFields,
      );

      if (Array.isArray(tableData) && tableData.length > 0) {
        // 应用过滤条件（如果有的话）
        const filteredData =
          filter.length > 0
            ? this.applyTableFilters(tableData, filter)
            : tableData;

        // 从表格数据中提取计算值
        const tableCalculationValues = this.extractTableCalculationValues(
          filteredData,
          tableFields,
          type,
        );

        calculationValues.push(...tableCalculationValues);
      }
    }

    if (calculationValues.length === 0) {
      formApi.setFieldValue(formApi.fieldName, defaultValue);
      return;
    }

    let result: number;

    switch (type) {
      case 'sum':
        result = calculationValues.reduce((sum, val) => sum + val, 0);
        break;
      case 'product':
        result = calculationValues.reduce((product, val) => product * val, 1);
        break;
      case 'subtract':
        result = calculationValues.reduce((diff, val, index) =>
          index === 0 ? val : diff - val,
        );
        break;
      case 'divide':
        result = calculationValues.reduce((quotient, val, index) =>
          index === 0 ? val : quotient / val,
        );
        break;
      case 'average':
        result =
          calculationValues.length > 0
            ? calculationValues.reduce((sum, val) => sum + val, 0) /
              calculationValues.length
            : defaultValue;
        break;
      case 'count':
        result = calculationValues.length > 0 ? calculationValues[0] : 0;
        break;
      case 'max':
        result =
          calculationValues.length > 0
            ? Math.max(...calculationValues)
            : defaultValue;
        break;
      case 'min':
        result =
          calculationValues.length > 0
            ? Math.min(...calculationValues)
            : defaultValue;
        break;
      case 'formula':
        result = this.executeFormula(calculationRule.formula || '', values);
        break;
      case 'custom':
        result = this.executeCustomFunction(
          calculationRule.customFunction || '',
          values,
        );
        break;
      default:
        result = defaultValue;
    }

    // 处理精度
    if (typeof result === 'number' && !isNaN(result)) {
      result = this.roundToPrecision(result, precision);
    } else {
      result = defaultValue;
    }

    // 设置计算结果 - 只有当结果与当前值不同时才更新
    const currentValue = formApi.getFieldValue?.(formApi.fieldName);
    if (currentValue !== result) {
      formApi.setFieldValue(formApi.fieldName, result);
    }
  },

  /**
   * 获取嵌套对象的值
   * @param obj 对象
   * @param path 路径（支持 a.b.c 格式）
   * @returns 值
   */
  getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  },

  /**
   * 解析数字值（增强版，处理快速输入）
   * @param value 值
   * @returns 数字
   */
  parseNumber(value: any): number {
    // 处理 null、undefined、空字符串
    if (value === null || value === undefined || value === '') {
      return 0;
    }

    // 如果已经是数字
    if (typeof value === 'number') {
      return isNaN(value) || !isFinite(value) ? 0 : value;
    }

    // 如果是字符串
    if (typeof value === 'string') {
      // 移除空格
      const trimmed = value.trim();

      if (trimmed === '' || trimmed === '-' || trimmed === '.') {
        return 0;
      }

      // 处理正在输入的数字（如 "123."、"-"、"12.3"）
      const parsed = parseFloat(trimmed);
      return isNaN(parsed) || !isFinite(parsed) ? 0 : parsed;
    }

    // 其他类型尝试转换
    const num = Number(value);
    return isNaN(num) || !isFinite(num) ? 0 : num;
  },

  /**
   * 执行公式计算
   * @param formula 公式字符串
   * @param values 表单值
   * @returns 计算结果
   */
  executeFormula(formula: string, values: Record<string, any>): number {
    try {
      // 替换公式中的变量
      let processedFormula = formula;
      Object.entries(values).forEach(([key, value]) => {
        const regex = new RegExp(`\\b${key}\\b`, 'g');
        processedFormula = processedFormula.replace(
          regex,
          String(this.parseNumber(value)),
        );
      });

      // 安全执行公式（仅支持基本数学运算）
      const safeFormula = processedFormula.replace(/[^0-9+\-*/.() ]/g, '');
      return new Function(`return ${safeFormula}`)();
    } catch (error) {
      console.warn('[联动计算] 公式执行失败:', error);
      return 0;
    }
  },

  /**
   * 执行自定义函数
   * @param functionName 函数名
   * @param values 表单值
   * @returns 计算结果
   */
  executeCustomFunction(
    functionName: string,
    values: Record<string, any>,
  ): number {
    try {
      // 这里可以扩展自定义计算函数
      const customFunctions: Record<string, (values: any) => number> = {
        // 示例：计算税后金额
        calculateAfterTax: (vals) => {
          const amount = this.parseNumber(vals.amount);
          const taxRate = this.parseNumber(vals.taxRate) / 100;
          return amount * (1 - taxRate);
        },
        // 示例：计算折扣价
        calculateDiscountPrice: (vals) => {
          const originalPrice = this.parseNumber(vals.originalPrice);
          const discount = this.parseNumber(vals.discount) / 100;
          return originalPrice * (1 - discount);
        },
      };

      const func = customFunctions[functionName];
      if (func) {
        return func(values);
      }

      console.warn(`[联动计算] 未找到自定义函数: ${functionName}`);
      return 0;
    } catch (error) {
      console.warn('[联动计算] 自定义函数执行失败:', error);
      return 0;
    }
  },

  /**
   * 数字精度处理
   * @param num 数字
   * @param precision 精度
   * @returns 处理后的数字
   */
  roundToPrecision(num: number, precision: number): number {
    if (isNaN(num) || !isFinite(num)) {
      return 0;
    }
    const factor = Math.pow(10, precision);
    return Math.round((num + Number.EPSILON) * factor) / factor;
  },

  /**
   * 从触发字段中获取表格数据
   * @param values 表单值
   * @param triggerFields 触发字段列表
   * @returns 表格数据数组
   */
  getTableDataFromTriggerFields(
    values: Record<string, any>,
    triggerFields: string[],
  ): any[] {
    // 遍历触发字段，找到包含表格数据的字段
    for (const fieldName of triggerFields) {
      const fieldValue = this.getNestedValue(values, fieldName);

      // 如果字段值是数组，认为是表格数据
      if (Array.isArray(fieldValue)) {
        return fieldValue;
      }
    }

    return [];
  },

  /**
   * 应用表格过滤条件
   * @param tableData 表格数据
   * @param filters 过滤条件
   * @returns 过滤后的数据
   */
  applyTableFilters(tableData: any[], filters: any[]): any[] {
    return tableData.filter((row) => {
      return filters.every((filter) => {
        const { field, operator, value } = filter;
        const rowValue = row[field];

        switch (operator) {
          case '=':
          case '==':
            return rowValue == value;
          case '!=':
            return rowValue != value;
          case '>':
            return Number(rowValue) > Number(value);
          case '>=':
            return Number(rowValue) >= Number(value);
          case '<':
            return Number(rowValue) < Number(value);
          case '<=':
            return Number(rowValue) <= Number(value);
          case 'in':
            return Array.isArray(value) && value.includes(rowValue);
          case 'not_in':
            return Array.isArray(value) && !value.includes(rowValue);
          case 'like':
            return String(rowValue).includes(String(value));
          case 'not_like':
            return !String(rowValue).includes(String(value));
          default:
            return true;
        }
      });
    });
  },

  /**
   * 从表格数据中提取计算值
   * @param tableData 表格数据
   * @param tableFields 表格字段列表
   * @param calculationType 计算类型
   * @returns 计算值数组
   */
  extractTableCalculationValues(
    tableData: any[],
    tableFields: string[],
    calculationType: string,
  ): number[] {
    const values: number[] = [];

    // 对于不同的计算类型，提取方式可能不同
    switch (calculationType) {
      case 'sum':
      case 'average':
        // 求和和平均值：提取所有字段的所有值
        tableFields.forEach((fieldName) => {
          tableData.forEach((row) => {
            const value = this.parseNumber(row[fieldName]);
            if (!isNaN(value)) {
              values.push(value);
            }
          });
        });
        break;

      case 'count':
        // 计数：返回符合条件的行数
        values.push(tableData.length);
        break;

      case 'max':
      case 'min':
        // 最大值/最小值：提取所有字段的所有值
        tableFields.forEach((fieldName) => {
          tableData.forEach((row) => {
            const value = this.parseNumber(row[fieldName]);
            if (!isNaN(value)) {
              values.push(value);
            }
          });
        });
        break;

      default:
        // 其他类型：提取所有字段的所有值
        tableFields.forEach((fieldName) => {
          tableData.forEach((row) => {
            const value = this.parseNumber(row[fieldName]);
            if (!isNaN(value)) {
              values.push(value);
            }
          });
        });
    }

    return values;
  },
};
