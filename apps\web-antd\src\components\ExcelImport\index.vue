<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  InboxOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue';
import {
  Button,
  Card,
  Divider,
  message,
  Progress,
  Result,
  Steps,
  Table,
  Tag,
  Upload,
} from 'ant-design-vue';
import * as XLSX from 'xlsx';

// 定义组件属性
interface Props {
  // 允许的文件类型
  accept?: string;
  // 最大文件大小（MB）
  maxSize?: number;
  // 表格列配置
  columns?: any[];
  // 数据验证函数
  validateData?: (data: any[]) => { errors: string[]; valid: boolean };
  // 数据转换函数
  transformData?: (data: any[]) => any[];
  // 模板生成函数
  generateTemplate?: () => { data: any[]; headers: string[] };
}

const props = withDefaults(defineProps<Props>(), {
  accept: '.csv,.xlsx,.xls',
  maxSize: 10,
  columns: () => [],
});

// 定义事件
const emit = defineEmits<{
  error: [error: string];
  success: [data: any[]];
}>();

// 步骤定义
const steps = [
  { title: '选择上传文件', description: '选择要导入的Excel文件' },
  { title: '选择要导入的数据', description: '预览并选择数据范围' },
  { title: '完成', description: '导入成功' },
];

// 响应式数据
const currentStep = ref(0);
const fileList = ref<any[]>([]);
const excelData = ref<any[]>([]);
const selectedSheet = ref('');
const sheetNames = ref<string[]>([]);
const importProgress = ref(0);
const isImporting = ref(false);
const importResult = ref<{
  errorCount: number;
  errors: string[];
  message: string;
  success: boolean;
  successCount: number;
}>({
  success: false,
  message: '',
  successCount: 0,
  errorCount: 0,
  errors: [],
});

// 创建Modal
const [Modal, modalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      resetState();
    }
  },
});

// 重置状态
const resetState = () => {
  currentStep.value = 0;
  fileList.value = [];
  excelData.value = [];
  selectedSheet.value = '';
  sheetNames.value = [];
  importProgress.value = 0;
  isImporting.value = false;
  importResult.value = {
    success: false,
    message: '',
    successCount: 0,
    errorCount: 0,
    errors: [],
  };
};

// 文件上传前的验证
const beforeUpload = (file: File) => {
  const isValidFile =
    file.type === 'text/csv' ||
    file.type ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel' ||
    file.name.endsWith('.csv') ||
    file.name.endsWith('.xlsx') ||
    file.name.endsWith('.xls');

  if (!isValidFile) {
    message.error('只能上传CSV或Excel文件！');
    return false;
  }

  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize;
  if (!isLtMaxSize) {
    message.error(`文件大小不能超过 ${props.maxSize}MB！`);
    return false;
  }

  return false; // 阻止自动上传，手动处理
};

// 处理文件选择
const handleFileChange = (info: any) => {
  const { file } = info;
  if (file.status === 'removed') {
    fileList.value = [];
    excelData.value = [];
    sheetNames.value = [];
    selectedSheet.value = '';
  } else {
    fileList.value = [file];
    readExcelFile(file.originFileObj || file);
  }
};

// 读取文件
const readExcelFile = (file: File) => {
  const fileName = file.name.toLowerCase();

  if (fileName.endsWith('.csv')) {
    readCSVFile(file);
  } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
    readExcelFileWithXLSX(file);
  } else {
    message.error('不支持的文件格式');
  }
};

// 读取CSV文件
const readCSVFile = (file: File) => {
  const reader = new FileReader();
  reader.addEventListener('load', (e) => {
    try {
      const text = e.target?.result as string;
      const lines = text.split('\n').filter((line) => line.trim());
      const data = lines.map((line) => {
        // 简单的CSV解析，支持逗号分隔
        return line
          .split(',')
          .map((cell) => cell.trim().replaceAll(/^"|"$/g, ''));
      });

      sheetNames.value = ['Sheet1'];
      selectedSheet.value = 'Sheet1';
      excelData.value = data;
      currentStep.value = 1;
    } catch (error) {
      message.error('CSV文件读取失败，请检查文件格式');
      console.error('CSV读取错误:', error);
    }
  });
  reader.readAsText(file, 'UTF-8');
};

// Excel文件读取
const readExcelFileWithXLSX = (file: File) => {
  const reader = new FileReader();
  reader.addEventListener('load', (e) => {
    try {
      const data = new Uint8Array(e.target?.result as ArrayBuffer);
      const workbook = XLSX.read(data, { type: 'array' });

      sheetNames.value = workbook.SheetNames;
      selectedSheet.value = workbook.SheetNames[0];

      parseSheetData(workbook, selectedSheet.value);
      currentStep.value = 1;
    } catch (error) {
      message.error('文件读取失败，请检查文件格式');
      console.error('Excel读取错误:', error);
    }
  });
  reader.readAsArrayBuffer(file);
};

// 解析工作表数据
const parseSheetData = (workbook: XLSX.WorkBook, sheetName: string) => {
  const worksheet = workbook.Sheets[sheetName];
  const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

  // 过滤空行
  const filteredData = jsonData.filter((row: any) =>
    row.some((cell: any) => cell !== null && cell !== undefined && cell !== ''),
  );

  excelData.value = filteredData;
};

// 切换工作表
const handleSheetChange = (sheetName: string) => {
  selectedSheet.value = sheetName;
  // CSV文件只有一个工作表，Excel文件需要重新解析
  if (
    fileList.value.length > 0 &&
    !fileList.value[0].name.toLowerCase().endsWith('.csv')
  ) {
    // Excel文件切换工作表逻辑
    const reader = new FileReader();
    reader.addEventListener('load', (e) => {
      const data = new Uint8Array(e.target?.result as ArrayBuffer);
      const workbook = XLSX.read(data, { type: 'array' });
      parseSheetData(workbook, sheetName);
    });
    reader.readAsArrayBuffer(
      fileList.value[0].originFileObj || fileList.value[0],
    );
  }
};

// 表格列配置
const tableColumns = computed(() => {
  if (excelData.value.length === 0) return [];

  const firstRow = excelData.value[0];
  return firstRow.map((header: any, index: number) => ({
    title: header || `列${index + 1}`,
    dataIndex: index,
    key: index,
    width: 120,
    ellipsis: true,
  }));
});

// 表格数据
const tableData = computed(() => {
  if (excelData.value.length <= 1) return [];

  return excelData.value.slice(1).map((row: any, index: number) => {
    const record: any = { key: index };
    row.forEach((cell: any, cellIndex: number) => {
      record[cellIndex] = cell;
    });
    return record;
  });
});

// 开始导入
const startImport = async () => {
  if (excelData.value.length <= 1) {
    message.warning('没有可导入的数据');
    return;
  }

  isImporting.value = true;
  importProgress.value = 0;

  try {
    // 转换数据格式
    let processedData = tableData.value;

    // 如果有数据转换函数，使用它
    if (props.transformData) {
      processedData = props.transformData(processedData);
    }

    // 如果有数据验证函数，使用它
    if (props.validateData) {
      const validation = props.validateData(processedData);
      if (!validation.valid) {
        importResult.value = {
          success: false,
          message: '数据验证失败',
          successCount: 0,
          errorCount: processedData.length,
          errors: validation.errors,
        };
        currentStep.value = 2;
        isImporting.value = false;
        return;
      }
    }

    // 模拟导入进度
    for (let i = 0; i <= 100; i += 10) {
      importProgress.value = i;
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    // 导入成功
    importResult.value = {
      success: true,
      message: '导入成功',
      successCount: processedData.length,
      errorCount: 0,
      errors: [],
    };

    emit('success', processedData);
    currentStep.value = 2;
  } catch (error) {
    importResult.value = {
      success: false,
      message: '导入失败',
      successCount: 0,
      errorCount: tableData.value.length,
      errors: [error instanceof Error ? error.message : '未知错误'],
    };
    emit('error', error instanceof Error ? error.message : '导入失败');
    currentStep.value = 2;
  } finally {
    isImporting.value = false;
  }
};

// 重新导入
const handleReImport = () => {
  resetState();
};

// 关闭弹窗
const handleClose = () => {
  modalApi.close();
};

// 下载导入模板
const downloadTemplate = () => {
  try {
    let templateData: { data: any[]; headers: string[] };

    // 如果有自定义模板生成函数，使用它
    if (props.generateTemplate) {
      templateData = props.generateTemplate();
    } else if (props.columns && props.columns.length > 0) {
      // 基于列配置生成模板
      const importableColumns = props.columns.filter(
        (col: any) =>
          col.field &&
          col.type !== 'action' &&
          col.type !== 'actions' &&
          col.field !== 'action' &&
          col.field !== 'actions',
      );

      templateData = {
        headers: importableColumns.map((col: any) => col.title || col.field),
        data: [
          importableColumns.map((col: any) => `示例${col.title || col.field}`),
        ],
      };
    } else {
      // 默认模板
      templateData = {
        headers: ['列1', '列2', '列3'],
        data: [['示例数据1', '示例数据2', '示例数据3']],
      };
    }

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    const worksheetData = [templateData.headers, ...templateData.data];
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // 设置列宽
    const colWidths = templateData.headers.map(() => ({ wch: 15 }));
    worksheet['!cols'] = colWidths;

    // 添加工作表
    XLSX.utils.book_append_sheet(workbook, worksheet, '导入模板');

    // 下载文件
    const fileName = `导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`;
    XLSX.writeFile(workbook, fileName);

    message.success('模板下载成功');
  } catch (error) {
    console.error('模板下载失败:', error);
    message.error('模板下载失败，请重试');
  }
};

// 暴露API
defineExpose({
  open: () => modalApi.open(),
  close: () => modalApi.close(),
  reset: resetState,
  getData: () => tableData.value,
  getCurrentStep: () => currentStep.value,
  modalApi,
});
</script>

<template>
  <Modal
    title="Excel数据导入"
    width="100vw"
    height="100vh"
    :footer="null"
    destroy-on-close
    fullscreen
    closable
  >
    <div class="excel-import-container">
      <!-- 步骤条 -->
      <Steps :current="currentStep" size="small" class="mb-4">
        <Steps.Step
          v-for="(step, index) in steps"
          :key="index"
          :title="step.title"
        />
      </Steps>

      <!-- 第一步：文件上传 -->
      <div v-if="currentStep === 0" class="step-content">
        <Card title="选择Excel文件" class="upload-card">
          <template #extra>
            <Button type="link" @click="downloadTemplate">
              下载导入模板
            </Button>
          </template>

          <Upload.Dragger
            v-model:file-list="fileList"
            :accept="props.accept"
            :before-upload="beforeUpload"
            @change="handleFileChange"
            :multiple="false"
            :show-upload-list="false"
            class="upload-dragger-compact"
          >
            <p class="ant-upload-drag-icon">
              <InboxOutlined style="font-size: 36px; color: #1890ff" />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">
              支持 .csv、.xlsx、.xls 格式，文件大小不超过 {{ props.maxSize }}MB
            </p>
          </Upload.Dragger>

          <div v-if="fileList.length > 0" class="mt-4">
            <div class="file-info">
              <UploadOutlined class="mr-2" />
              <span>{{ fileList[0].name }}</span>
              <Button
                type="link"
                size="small"
                @click="
                  fileList = [];
                  excelData = [];
                  currentStep = 0;
                "
              >
                重新选择
              </Button>
            </div>
          </div>
        </Card>
      </div>

      <!-- 第二步：数据预览 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="two-column-layout">
          <!-- 左侧：表单信息 -->
          <div class="left-panel">
            <Card title="导入设置" size="small" class="settings-card">
              <!-- 工作表选择 -->
              <div class="form-section">
                <div class="form-label">工作表选择</div>
                <div class="form-content">
                  <Button.Group v-if="sheetNames.length > 1" size="small">
                    <Button
                      v-for="sheetName in sheetNames"
                      :key="sheetName"
                      :type="
                        selectedSheet === sheetName ? 'primary' : 'default'
                      "
                      size="small"
                      @click="handleSheetChange(sheetName)"
                    >
                      {{ sheetName }}
                    </Button>
                  </Button.Group>
                  <Tag v-else color="blue" size="small">
                    {{ selectedSheet }}
                  </Tag>
                </div>
              </div>

              <!-- 数据统计 -->
              <div class="form-section">
                <div class="form-label">数据统计</div>
                <div class="form-content">
                  <div class="stat-item">
                    <span class="stat-label">总行数：</span>
                    <span class="stat-value">{{ excelData.length }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">数据行：</span>
                    <span class="stat-value">{{
                      Math.max(0, excelData.length - 1)
                    }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">列数：</span>
                    <span class="stat-value">{{ tableColumns.length }}</span>
                  </div>
                </div>
              </div>

              <!-- 导入选项 -->
              <div class="form-section">
                <div class="form-label">导入选项</div>
                <div class="form-content">
                  <div class="option-item">
                    <span>包含表头</span>
                    <Tag color="green" size="small">是</Tag>
                  </div>
                  <div class="option-item">
                    <span>数据验证</span>
                    <Tag color="blue" size="small">启用</Tag>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="form-actions">
                <Button
                  @click="currentStep = 0"
                  size="small"
                  block
                  class="mb-2"
                >
                  上一步
                </Button>
                <Button
                  type="primary"
                  size="small"
                  block
                  @click="startImport"
                  :disabled="tableData.length === 0"
                >
                  开始导入
                </Button>
              </div>
            </Card>
          </div>

          <!-- 右侧：数据表格 -->
          <div class="right-panel">
            <Card title="数据预览" size="small" class="table-card">
              <div class="table-container-two-column">
                <Table
                  :columns="tableColumns"
                  :data-source="tableData"
                  :pagination="{
                    pageSize: 100,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    size: 'small',
                    showTotal: (total, range) =>
                      `${range[0]}-${range[1]} / ${total}`,
                  }"
                  :scroll="{ x: 'max-content', y: 'calc(100vh - 250px)' }"
                  size="small"
                  bordered
                />
              </div>
            </Card>
          </div>
        </div>
      </div>

      <!-- 第三步：导入结果 -->
      <div v-if="currentStep === 2" class="step-content">
        <Card title="导入结果" class="result-card">
          <!-- 导入进度 -->
          <div v-if="isImporting" class="import-progress">
            <div class="mb-4 text-center">
              <div class="mb-2 text-lg">正在导入数据...</div>
              <Progress :percent="importProgress" :show-info="true" />
            </div>
          </div>

          <!-- 导入结果 -->
          <div v-else class="import-result">
            <Result
              :status="importResult.success ? 'success' : 'error'"
              :title="importResult.message"
            >
              <template #icon>
                <CheckCircleOutlined
                  v-if="importResult.success"
                  style="color: #52c41a"
                />
                <CloseCircleOutlined v-else style="color: #ff4d4f" />
              </template>

              <template #extra>
                <div class="result-stats mb-4">
                  <div v-if="importResult.success" class="success-stats">
                    <p>
                      成功导入
                      <strong>{{ importResult.successCount }}</strong> 条数据
                    </p>
                  </div>
                  <div v-else class="error-stats">
                    <p>
                      导入失败，共
                      <strong>{{ importResult.errorCount }}</strong>
                      条数据未能导入
                    </p>
                    <div
                      v-if="importResult.errors.length > 0"
                      class="error-details mt-2"
                    >
                      <Divider>错误详情</Divider>
                      <ul class="error-list">
                        <li
                          v-for="(error, index) in importResult.errors"
                          :key="index"
                          class="error-item"
                        >
                          {{ error }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div class="result-actions">
                  <Button
                    v-if="!importResult.success"
                    type="primary"
                    @click="handleReImport"
                    class="mr-2"
                  >
                    重新导入
                  </Button>
                  <Button @click="handleClose">
                    {{ importResult.success ? '完成' : '关闭' }}
                  </Button>
                </div>
              </template>
            </Result>
          </div>
        </Card>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  .sheet-selector {
    flex-direction: column;
    align-items: flex-start;
  }

  .sheet-selector span {
    margin-bottom: 8px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .result-actions {
    flex-direction: column;
  }
}

.excel-import-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 16px;
  overflow: hidden;
}

.step-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  margin-top: 16px;
  overflow: hidden;
}

/* 两列布局 */
.two-column-layout {
  display: flex;
  flex: 1;
  gap: 16px;
  overflow: hidden;
}

.left-panel {
  flex-shrink: 0;
  width: 320px;
  overflow-y: auto;
}

.right-panel {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

.settings-card,
.table-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.settings-card .ant-card-body,
.table-card .ant-card-body {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

.upload-card,
.preview-card,
.result-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
}

.preview-card .ant-card-body {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

.upload-dragger-compact {
  min-height: 120px !important;
}

.upload-dragger-compact .ant-upload-drag-icon {
  margin-bottom: 8px !important;
}

.upload-dragger-compact .ant-upload-text {
  margin-bottom: 4px !important;
  font-size: 14px !important;
}

.upload-dragger-compact .ant-upload-hint {
  font-size: 12px !important;
}

.file-info {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
}

.sheet-info-bar {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.sheet-selector-compact {
  display: flex;
  align-items: center;
}

.data-stats-compact {
  font-size: 13px;
}

.sheet-selector {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.sheet-tag {
  padding: 4px 12px;
  font-size: 14px;
}

.data-stats {
  padding: 12px;
  background-color: #fafafa;
  border-left: 4px solid #1890ff;
  border-radius: 6px;
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 20px;
}

.form-label {
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 600;
  color: #262626;
}

.form-content {
  margin-bottom: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 12px;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: 600;
  color: #1890ff;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 12px;
}

.form-actions {
  padding-top: 16px;
  margin-top: auto;
  border-top: 1px solid #f0f0f0;
}

/* 表格容器 */
.table-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.table-container-two-column {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

.table-container-two-column .ant-table-wrapper {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.table-container-two-column .ant-table {
  flex: 1;
}

.table-container-two-column .ant-table-pagination {
  flex-shrink: 0;
  padding: 12px 0;
  margin: 0;
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
}

/* 确保分页器在两列布局中正确显示 */
.table-card .ant-card-body {
  padding-bottom: 0;
}

.table-container-two-column .ant-table-container {
  flex: 1;
  overflow: auto;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.action-buttons-compact {
  display: flex;
  flex-shrink: 0;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.import-progress {
  padding: 40px 20px;
}

.result-stats {
  text-align: center;
}

.success-stats p {
  font-size: 16px;
  color: #52c41a;
}

.error-stats p {
  font-size: 16px;
  color: #ff4d4f;
}

.error-details {
  max-width: 600px;
  margin: 0 auto;
  text-align: left;
}

.error-list {
  max-height: 200px;
  padding: 16px;
  margin: 0;
  overflow-y: auto;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
}

.error-item {
  margin-bottom: 8px;
  margin-left: 20px;
  color: #ff4d4f;
  list-style-type: disc;
}

.error-item:last-child {
  margin-bottom: 0;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
