<script setup lang="ts">
import { onUnmounted, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';

const emit = defineEmits(['modalConfirm']);

const data = ref<any>();

// 创建表单实例
const [Form, formApi] = useVbenForm({
  wrapperClass: 'grid-cols-3',
  showDefaultActions: false,
  commonConfig: {
    labelWidth: 150,
  },
});

// 处理确认操作
const handleConfirm = async () => {
  // 如果有表单，先验证
  if (data.value?.schema) {
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }

    const values = await formApi.getValues();
    // 这里可以通过回调或者事件来处理表单数据
    emit('modalConfirm', values);
  }

  modalApi.close();
};

// 处理取消操作
const handleCancel = () => {
  modalApi.close();
};

const [Modal, modalApi] = useVbenModal({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();

      // 如果有表单配置，设置表单
      if (data.value?.schema) {
        formApi.setState({ schema: data.value.schema });

        // 如果有表单数据，设置表单值
        if (data.value?.formData) {
          formApi.setValues(data.value.formData);
        }
      }
    }
  },
});

// 监听上传状态变化，控制提交按钮状态
watch(
  () => uploadStore.isAnyUploading,
  (isUploading: boolean) => {
    modalApi.setState({
      confirmLoading: isUploading,
    });
  },
  { immediate: true },
);

// 组件卸载时清理上传状态
onUnmounted(() => {
  uploadStore.clearAllStates();
});
</script>

<template>
  <Modal class="w-[1200px]">
    <!-- 表单内容 -->
    <div v-if="data?.schema" class="modal-form-content">
      <Form />
    </div>

    <!-- 其他内容 -->
    <div v-else class="modal-content">
      <slot></slot>
    </div>
  </Modal>
</template>

<style scoped>
.common-modal-content {
  min-height: 50px;
}

/* 根据类型设置不同的样式 */
.modal-info .common-modal-content {
  padding-left: 12px;
  border-left: 4px solid #1890ff;
}

.modal-success .common-modal-content {
  padding-left: 12px;
  border-left: 4px solid #52c41a;
}

.modal-warning .common-modal-content {
  padding-left: 12px;
  border-left: 4px solid #faad14;
}

.modal-error .common-modal-content {
  padding-left: 12px;
  border-left: 4px solid #ff4d4f;
}
</style>
