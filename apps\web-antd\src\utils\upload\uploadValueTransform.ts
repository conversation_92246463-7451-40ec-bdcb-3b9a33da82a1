/**
 * Upload 组件值转换工具函数
 */

/**
 * 将 Upload 组件的文件对象数组转换为路径字符串数组
 * @param fileList Upload 组件的文件列表
 * @param multiple 是否多选模式
 * @returns 路径字符串或路径字符串数组
 */
export function transformUploadValue(fileList: any[], multiple: boolean = true): string | string[] | null {
  if (!fileList || !Array.isArray(fileList)) {
    return multiple ? [] : null;
  }

  // 转换文件对象数组为路径字符串数组
  const paths = fileList
    .filter((file: any) => file && file.status === 'done')
    .map((file: any) => {
      // 优先使用 response.path，其次使用 response（如果是字符串），最后使用 url
      if (file.response && typeof file.response === 'object' && file.response.path) {
        return file.response.path;
      } else if (file.response && typeof file.response === 'string') {
        return file.response;
      } else if (file.url) {
        return file.url;
      }
      return null;
    })
    .filter(Boolean);

  // 如果只有一个文件且不是多选，返回字符串而不是数组
  if (paths.length === 1 && !multiple) {
    return paths[0];
  }

  return paths;
}

/**
 * 将路径字符串转换为 Upload 组件的文件对象
 * @param path 文件路径
 * @param index 文件索引（用于生成 uid）
 * @returns Upload 文件对象
 */
export function pathToFileObject(path: string, index: number = 0): any {
  if (!path) return null;

  return {
    uid: `default-${Date.now()}-${index}`,
    name: path.split('/').pop() || `文件${index + 1}`,
    status: 'done',
    url: path,
    response: path,
  };
}

/**
 * 将路径字符串数组转换为 Upload 组件的文件对象数组
 * @param paths 路径字符串数组
 * @returns Upload 文件对象数组
 */
export function pathsToFileList(paths: string | string[]): any[] {
  if (!paths) return [];

  if (typeof paths === 'string') {
    const fileObj = pathToFileObject(paths);
    return fileObj ? [fileObj] : [];
  }

  if (Array.isArray(paths)) {
    return paths
      .map((path, index) => pathToFileObject(path, index))
      .filter(Boolean);
  }

  return [];
}

/**
 * 处理表单数据中的 Upload 字段值转换
 * @param formData 表单数据
 * @param uploadFields Upload 字段配置数组
 * @returns 处理后的表单数据
 */
export function processUploadFieldsInFormData(
  formData: Record<string, any>,
  uploadFields: Array<{ fieldName: string; multiple: boolean }>
): Record<string, any> {
  const processedData = { ...formData };

  uploadFields.forEach(({ fieldName, multiple }) => {
    if (processedData[fieldName]) {
      processedData[fieldName] = transformUploadValue(processedData[fieldName], multiple);
    }
  });

  return processedData;
}
