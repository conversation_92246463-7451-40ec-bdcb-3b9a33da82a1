<template>
  <div class="p-6">
    <h1 class="mb-6 text-2xl font-bold">简化上传组件测试</h1>

    <!-- 直接使用 Upload 组件测试 -->
    <div class="mb-8">
      <h2 class="mb-4 text-lg font-semibold">
        直接 Upload 组件测试（multiple=true, maxCount=5）
      </h2>
      <Upload
        v-model:file-list="fileList"
        :custom-request="handleUpload"
        :multiple="true"
        list-type="picture-card"
        :max-count="5"
        @change="handleChange"
      >
        <div>
          <plus-outlined />
          <div style="margin-top: 8px">Upload</div>
        </div>
      </Upload>

      <div class="mt-4 rounded bg-yellow-100 p-4">
        <p><strong>测试步骤：</strong></p>
        <ol class="mt-2 list-inside list-decimal">
          <li>选择第一个图片文件上传</li>
          <li>等待上传完成</li>
          <li>再选择第二个图片文件上传</li>
          <li>查看是否两个文件都显示</li>
          <li>查看 Console 中的详细日志</li>
        </ol>
      </div>

      <div class="mt-4">
        <h3 class="font-medium">当前文件列表:</h3>
        <pre class="mt-2 bg-gray-100 p-2 text-sm">{{
          JSON.stringify(fileList, null, 2)
        }}</pre>
      </div>

      <div class="mt-4">
        <h3 class="font-medium">提取的 URL 数组:</h3>
        <pre class="mt-2 bg-blue-100 p-2 text-sm">{{
          JSON.stringify(extractUrls(fileList), null, 2)
        }}</pre>
      </div>
    </div>

    <!-- 表单测试 -->
    <div class="mb-8">
      <h2 class="mb-4 text-lg font-semibold">表单集成测试</h2>
      <VbenForm :schema="formSchema" @submit="handleSubmit" class="w-full" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Upload, message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { VbenForm } from '@vben/common-ui';
import type { VbenFormSchema } from '#/adapter/form';
import { baseRequestClient } from '#/api/request';

// 直接测试的文件列表
const fileList = ref<any[]>([]);

// 模拟上传函数
async function handleUpload(options: any) {
  const { file, onSuccess, onError, onProgress } = options;

  console.log('=== 开始上传文件 ===');
  console.log('文件信息:', file);
  console.log('当前文件列表长度:', fileList.value.length);
  console.log('文件 UID:', file.uid);

  try {
    onProgress?.({ percent: 0 });

    // 模拟上传延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    onProgress?.({ percent: 50 });

    // 再等一下，模拟真实上传
    await new Promise((resolve) => setTimeout(resolve, 500));

    onProgress?.({ percent: 100 });

    // 模拟返回的 URL
    const mockUrl = `https://example.com/uploads/${Date.now()}-${file.name}`;
    console.log('上传成功，返回 URL:', mockUrl);
    console.log('调用 onSuccess，参数:', { response: mockUrl, file: file.name });

    // 关键：确保 onSuccess 调用正确
    onSuccess?.(mockUrl, file);

    console.log('onSuccess 调用完成');
    message.success(`${file.name} 上传成功`);

    // 上传成功后检查文件列表
    setTimeout(() => {
      console.log('=== 上传完成后状态检查 ===');
      console.log('文件列表长度:', fileList.value.length);
      console.log('文件列表详情:', fileList.value.map(f => ({
        name: f.name,
        status: f.status,
        uid: f.uid,
        response: f.response
      })));
    }, 200);
  } catch (error) {
    console.error('上传失败:', error);
    onError?.(error);
    message.error(`${file.name} 上传失败`);
  }
}

// 处理文件列表变化
function handleChange(info: any) {
  console.log('=== 文件列表变化 ===');
  console.log('onChange 触发，info:', info);
  console.log('新的 fileList 长度:', info.fileList?.length || 0);
  console.log('新的 fileList:', info.fileList);

  // 检查每个文件的状态
  if (info.fileList) {
    info.fileList.forEach((file: any, index: number) => {
      console.log(`文件 ${index}:`, {
        name: file.name,
        status: file.status,
        response: file.response,
        uid: file.uid,
      });
    });
  }
}

// 提取 URL 数组的函数
function extractUrls(fileList: any[]): string[] {
  return fileList
    .filter((file) => file.status === 'done')
    .map((file) => {
      if (typeof file.response === 'string') {
        return file.response;
      }
      if (file.response && typeof file.response === 'object') {
        return (
          file.response.data || file.response.url || file.response.path || ''
        );
      }
      return file.url || '';
    })
    .filter(Boolean);
}

// 表单 Schema
const formSchema = computed<VbenFormSchema[]>(() => [
  {
    fieldName: 'images',
    label: '图片上传',
    component: 'Upload',
    componentProps: {
      accept: '.png,.jpg,.jpeg',
      multiple: true,
      listType: 'picture-card',
      maxCount: 5,
      customRequest: handleUpload,
    },
    defaultValue: [],
  },
]);

// 表单提交处理
function handleSubmit(values: Record<string, any>) {
  console.log('=== 表单提交数据 ===');
  console.log('原始数据:', values);

  // 手动提取 URL 数组
  if (values.images && Array.isArray(values.images)) {
    const urls = extractUrls(values.images);
    console.log('提取的 URL 数组:', urls);

    // 这就是我们想要的最终数据格式
    const finalData = {
      ...values,
      images: urls,
    };
    console.log('最终提交数据:', finalData);
  }

  message.success('查看 Console 中的数据格式');
}
</script>
