<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">简化上传组件测试</h1>
    
    <!-- 直接使用 Upload 组件测试 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">直接 Upload 组件测试</h2>
      <Upload
        v-model:file-list="fileList"
        :custom-request="handleUpload"
        :multiple="true"
        list-type="picture-card"
        :max-count="5"
      >
        <div>
          <plus-outlined />
          <div style="margin-top: 8px">Upload</div>
        </div>
      </Upload>
      
      <div class="mt-4">
        <h3 class="font-medium">当前文件列表:</h3>
        <pre class="bg-gray-100 p-2 mt-2 text-sm">{{ JSON.stringify(fileList, null, 2) }}</pre>
      </div>
      
      <div class="mt-4">
        <h3 class="font-medium">提取的 URL 数组:</h3>
        <pre class="bg-blue-100 p-2 mt-2 text-sm">{{ JSON.stringify(extractUrls(fileList), null, 2) }}</pre>
      </div>
    </div>

    <!-- 表单测试 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-4">表单集成测试</h2>
      <VbenForm
        :schema="formSchema"
        @submit="handleSubmit"
        class="w-full"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Upload, message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { VbenForm } from '@vben/common-ui';
import type { VbenFormSchema } from '#/adapter/form';
import { baseRequestClient } from '#/api/request';

// 直接测试的文件列表
const fileList = ref<any[]>([]);

// 模拟上传函数
async function handleUpload(options: any) {
  const { file, onSuccess, onError, onProgress } = options;
  
  try {
    onProgress?.({ percent: 0 });
    
    // 模拟上传延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    onProgress?.({ percent: 100 });
    
    // 模拟返回的 URL
    const mockUrl = `https://example.com/uploads/${file.name}`;
    onSuccess?.(mockUrl, file);
    
    message.success(`${file.name} 上传成功`);
  } catch (error) {
    onError?.(error);
    message.error(`${file.name} 上传失败`);
  }
}

// 提取 URL 数组的函数
function extractUrls(fileList: any[]): string[] {
  return fileList
    .filter(file => file.status === 'done')
    .map(file => {
      if (typeof file.response === 'string') {
        return file.response;
      }
      if (file.response && typeof file.response === 'object') {
        return file.response.data || file.response.url || file.response.path || '';
      }
      return file.url || '';
    })
    .filter(Boolean);
}

// 表单 Schema
const formSchema = computed<VbenFormSchema[]>(() => [
  {
    fieldName: 'images',
    label: '图片上传',
    component: 'Upload',
    componentProps: {
      accept: '.png,.jpg,.jpeg',
      multiple: true,
      listType: 'picture-card',
      maxCount: 5,
      customRequest: handleUpload,
    },
    defaultValue: [],
  },
]);

// 表单提交处理
function handleSubmit(values: Record<string, any>) {
  console.log('=== 表单提交数据 ===');
  console.log('原始数据:', values);
  
  // 手动提取 URL 数组
  if (values.images && Array.isArray(values.images)) {
    const urls = extractUrls(values.images);
    console.log('提取的 URL 数组:', urls);
    
    // 这就是我们想要的最终数据格式
    const finalData = {
      ...values,
      images: urls
    };
    console.log('最终提交数据:', finalData);
  }
  
  message.success('查看 Console 中的数据格式');
}
</script>
