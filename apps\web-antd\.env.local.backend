# 本地后端环境配置（此文件不会被提交到 Git）

# ================================
# 代理配置 - 本地后端
# ================================

VITE_PROXY_TARGET=http://127.0.0.1:8000
VITE_PROXY_REWRITE_FROM=^/api
VITE_PROXY_REWRITE_TO=
VITE_PROXY_PATH_PREFIX=/api
VITE_PROXY_CHANGE_ORIGIN=true
VITE_PROXY_WS=true

# ================================
# 其他配置
# ================================

# API 地址
VITE_GLOB_API_URL=/api
VITE_GLOB_OTHER_API_URL=https://mock-napi.vben.pro/other-api

# 是否跳过 token refresh 接口（本地后端使用）
VITE_SKIP_TOKEN_REFRESH=true
