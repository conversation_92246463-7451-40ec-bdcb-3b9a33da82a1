#!/usr/bin/env node

/**
 * 项目清理脚本
 * 用于清理项目中不必要的调试打印、测试文件和文档
 */

import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 需要清理的调试打印模式
const DEBUG_PATTERNS = [
  // 调试打印
  /console\.(log|warn|error)\s*\(\s*['"`]\[.*?\].*?\)/g,
  /console\.(log|warn|error)\s*\(\s*['"`].*?调试.*?\)/g,
  /console\.(log|warn|error)\s*\(\s*['"`].*?debug.*?\)/gi,
  
  // 特定的调试语句
  /console\.warn\s*\(\s*['"`]\[EditTable.*?\]/g,
  /console\.warn\s*\(\s*['"`]\[general-editing-drawer\].*?\]/g,
  /console\.warn\s*\(\s*['"`]\[表格计算.*?\]/g,
  /console\.warn\s*\(\s*['"`]\[联动.*?\]/g,
  /console\.log\s*\(\s*['"`]\[联动计算\].*?\]/g,
];

// 需要保留的 console 语句（错误处理等）
const KEEP_PATTERNS = [
  /console\.error\s*\(\s*['"`].*?失败.*?\)/,
  /console\.error\s*\(\s*['"`]Error.*?\)/,
  /console\.warn\s*\(\s*['"`]Warning.*?\)/,
];

// 需要清理的文件和目录
const CLEANUP_TARGETS = [
  // 测试文件
  'apps/web-antd/src/utils/date-format-test.ts',
  
  // 文档文件（保留重要文档）
  'apps/web-antd/docs/api-standards.md',
  'docs/frontend-unified-guide.md',
  
  // 临时文件
  'apps/web-antd/scripts/update-api-exports.mjs',
  'apps/web-antd/scripts/switch-proxy.js',
];

// 需要清理调试打印的目录
const CLEANUP_DIRS = [
  'apps/web-antd/src/components',
  'apps/web-antd/src/utils',
];

/**
 * 检查文件是否应该被处理
 */
function shouldProcessFile(filePath) {
  return filePath.endsWith('.vue') || 
         filePath.endsWith('.ts') || 
         filePath.endsWith('.js') ||
         filePath.endsWith('.mjs');
}

/**
 * 清理文件中的调试打印
 */
async function cleanupDebugPrints(filePath) {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    let cleanedContent = content;
    let hasChanges = false;

    // 按行处理，保持更好的控制
    const lines = content.split('\n');
    const cleanedLines = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      let shouldKeepLine = true;

      // 检查是否是需要清理的调试打印
      for (const pattern of DEBUG_PATTERNS) {
        if (pattern.test(line)) {
          // 检查是否是需要保留的语句
          const shouldKeep = KEEP_PATTERNS.some(keepPattern => keepPattern.test(line));
          if (!shouldKeep) {
            shouldKeepLine = false;
            hasChanges = true;
            console.log(`🗑️  移除调试打印: ${filePath}:${i + 1}`);
            console.log(`    ${line.trim()}`);
            break;
          }
        }
      }

      if (shouldKeepLine) {
        cleanedLines.push(line);
      }
    }

    if (hasChanges) {
      cleanedContent = cleanedLines.join('\n');
      await fs.writeFile(filePath, cleanedContent, 'utf-8');
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 递归处理目录
 */
async function processDirectory(dirPath) {
  try {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });
    let totalChanges = 0;

    for (const entry of entries) {
      const fullPath = join(dirPath, entry.name);

      if (entry.isDirectory()) {
        // 跳过 node_modules 和其他不需要处理的目录
        if (!['node_modules', '.git', 'dist', 'build'].includes(entry.name)) {
          totalChanges += await processDirectory(fullPath);
        }
      } else if (entry.isFile() && shouldProcessFile(fullPath)) {
        const changed = await cleanupDebugPrints(fullPath);
        if (changed) totalChanges++;
      }
    }

    return totalChanges;
  } catch (error) {
    console.error(`❌ 处理目录失败: ${dirPath}`, error.message);
    return 0;
  }
}

/**
 * 删除指定的文件和目录
 */
async function removeTargets() {
  let removedCount = 0;

  for (const target of CLEANUP_TARGETS) {
    const targetPath = join(projectRoot, target);
    
    try {
      const stat = await fs.stat(targetPath);
      
      if (stat.isDirectory()) {
        await fs.rmdir(targetPath, { recursive: true });
        console.log(`🗂️  删除目录: ${target}`);
      } else {
        await fs.unlink(targetPath);
        console.log(`📄 删除文件: ${target}`);
      }
      
      removedCount++;
    } catch (error) {
      if (error.code !== 'ENOENT') {
        console.error(`❌ 删除失败: ${target}`, error.message);
      }
    }
  }

  return removedCount;
}

/**
 * 主函数
 */
async function main() {
  console.log('🧹 开始清理项目...\n');

  // 1. 清理调试打印
  console.log('📝 清理调试打印语句...');
  let totalChanges = 0;

  for (const dir of CLEANUP_DIRS) {
    const dirPath = join(projectRoot, dir);
    try {
      const changes = await processDirectory(dirPath);
      totalChanges += changes;
      console.log(`✅ 处理目录: ${dir} (${changes} 个文件已修改)`);
    } catch (error) {
      console.error(`❌ 处理目录失败: ${dir}`, error.message);
    }
  }

  // 2. 删除不必要的文件
  console.log('\n🗑️  删除不必要的文件...');
  const removedCount = await removeTargets();

  // 3. 总结
  console.log('\n📊 清理完成！');
  console.log(`   - 修改了 ${totalChanges} 个文件的调试打印`);
  console.log(`   - 删除了 ${removedCount} 个文件/目录`);
  
  if (totalChanges > 0 || removedCount > 0) {
    console.log('\n💡 建议运行以下命令检查项目状态:');
    console.log('   pnpm run format  # 格式化代码');
    console.log('   pnpm run build   # 构建项目');
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { cleanupDebugPrints, processDirectory, removeTargets };
