<template>
  <div class="p-4">
    <h2 class="mb-4 text-lg font-semibold">新 Upload 组件测试</h2>
    
    <!-- 表单测试 -->
    <VbenForm
      :schema="formSchema"
      @submit="handleSubmit"
      class="mb-8"
    />
    
    <!-- 当前表单值显示 -->
    <div class="p-4 bg-gray-100 rounded">
      <h3 class="mb-2 font-medium">当前表单值：</h3>
      <pre>{{ JSON.stringify(formValues, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { VbenForm } from '#/adapter/form';
import type { VbenFormSchema } from '#/adapter/form';
import { transformBackendSearchToSchema } from '#/utils/search-schema';
import { message } from 'ant-design-vue';

// 表单值
const formValues = ref({});

// 模拟后端数据
const backendFields = [
  {
    field: 'avatar',
    title: '头像上传（单文件）',
    type: 'Upload',
    config: {
      listType: 'picture-card',
      maxCount: 1,
      multiple: false,
      uploadText: '上传头像',
    },
  },
  {
    field: 'gallery',
    title: '图片库（多文件，默认配置）',
    type: 'Upload',
    default: [
      'https://via.placeholder.com/150x150.png?text=Image1',
      'https://via.placeholder.com/150x150.png?text=Image2'
    ],
    config: {
      uploadText: '上传图片',
    },
  },
  {
    field: 'documents',
    title: '文档上传（文本样式）',
    type: 'Upload',
    config: {
      listType: 'text',
      accept: '.pdf,.doc,.docx',
      uploadText: '上传文档',
    },
  },
  {
    field: 'files',
    title: '文件上传（限制数量）',
    type: 'Upload',
    config: {
      maxCount: 3,
      uploadText: '最多上传3个文件',
    },
  },
  {
    field: 'disabledUpload',
    title: '禁用上传',
    type: 'Upload',
    config: {
      disabled: true,
      uploadText: '已禁用',
    },
  },
];

// 使用 transformBackendSearchToSchema 转换
const formSchema = computed(() => {
  const schema = transformBackendSearchToSchema(backendFields);
  console.log('转换后的 schema:', schema);
  return schema;
});

// 处理表单提交
const handleSubmit = (values: any) => {
  console.log('表单提交:', values);
  formValues.value = values;
  message.success('表单提交成功！请查看控制台和下方显示的值');
};
</script>
