#!/usr/bin/env node

/**
 * 后端环境切换脚本
 * 用于在本地后端和远程后端之间切换
 */

const fs = require('fs');
const path = require('path');

const ENV_LOCAL_FILE = '.env.local';
const ENV_LOCAL_BACKEND_FILE = '.env.local.backend';
const ENV_LOCAL_REMOTE_FILE = '.env.local.remote';

// 远程后端配置
const REMOTE_CONFIG = `# 本地环境配置（此文件不会被提交到 Git）

# ================================
# 代理配置 - 远程后端
# ================================

VITE_PROXY_TARGET=https://erp.gbuilderchina.com/testapiv2/
VITE_PROXY_REWRITE_FROM=^/api
VITE_PROXY_REWRITE_TO=
VITE_PROXY_PATH_PREFIX=/api
VITE_PROXY_CHANGE_ORIGIN=true
VITE_PROXY_WS=true

# ================================
# 其他配置
# ================================

# API 地址
VITE_GLOB_API_URL=/api
VITE_GLOB_OTHER_API_URL=https://mock-napi.vben.pro/other-api`;

function switchToBackend(type) {
  let sourceFile, targetConfig;

  if (type === 'local') {
    sourceFile = ENV_LOCAL_BACKEND_FILE;
    if (!fs.existsSync(sourceFile)) {
      console.error(`❌ 本地后端配置文件 ${sourceFile} 不存在`);
      process.exit(1);
    }
    targetConfig = fs.readFileSync(sourceFile, 'utf8');
    console.log('🔄 切换到本地后端...');
  } else if (type === 'remote') {
    targetConfig = REMOTE_CONFIG;
    // 保存远程配置到文件
    fs.writeFileSync(ENV_LOCAL_REMOTE_FILE, targetConfig);
    console.log('🔄 切换到远程后端...');
  } else {
    console.error('❌ 无效的后端类型，请使用 local 或 remote');
    process.exit(1);
  }

  // 写入配置到 .env.local
  fs.writeFileSync(ENV_LOCAL_FILE, targetConfig);

  console.log(`✅ 已切换到 ${type === 'local' ? '本地' : '远程'} 后端`);
  console.log('📝 请重启开发服务器以使配置生效');
}

// 获取命令行参数
const args = process.argv.slice(2);
const command = args[0];

if (!command) {
  console.log(`
🔧 后端环境切换工具

用法:
  node switch-backend.js local   # 切换到本地后端
  node switch-backend.js remote  # 切换到远程后端

当前配置:
  本地后端: ${fs.existsSync(ENV_LOCAL_BACKEND_FILE) ? '✅ 已配置' : '❌ 未配置'}
  远程后端: ✅ 已配置
`);
  process.exit(0);
}

switchToBackend(command);
