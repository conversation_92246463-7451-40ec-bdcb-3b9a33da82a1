# EditTable 外部联动功能

## 🎉 新功能发布

我们为 EditTable 组件新增了**外部联动功能**，允许根据外部表单字段的值动态控制 edittable 内部编辑表单的字段显示/隐藏和必填状态。

## 🚀 快速开始

### 1. 基本配置

```php
<?php
$edittableField = [
    'field' => 'order_items',
    'title' => '订单明细',
    'type' => 'edittable',
    'config' => [
        'columns' => [...],
        'form' => [...],
        // 新增：外部联动配置
        'externalLinkage' => [
            'triggerFields' => ['order_type'], // 监听外部字段
            'rules' => [
                'visibility' => [
                    'targetFields' => [
                        [
                            'field' => 'wholesale_price',
                            'showWhen' => [
                                [
                                    'field' => 'order_type',
                                    'operator' => '=',
                                    'value' => 'wholesale'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ]
];
?>
```

### 2. 支持的功能

- ✅ **字段显示/隐藏**：根据外部字段值控制内部字段的显示状态
- ✅ **必填状态控制**：根据外部字段值动态设置字段必填
- ✅ **多种操作符**：支持 =, !=, >, <, >=, <=, in, not_in
- ✅ **多字段监听**：可同时监听多个外部字段
- ✅ **实时响应**：外部字段变化时立即生效

### 3. 应用场景

- 🛒 **电商订单**：根据订单类型显示不同价格字段
- 👥 **人力资源**：根据员工类型显示不同薪资字段
- 📊 **项目管理**：根据项目类型显示相应配置字段
- 💰 **财务管理**：根据业务类型控制税务字段

## 📚 详细文档

- [完整使用指南](./edittable-external-linkage-guide.md)
- [PHP 后端配置](./php-backend-unified-guide.md#12121-可编辑表格外部联动-edittable-externallinkage)
- [前端使用说明](./frontend-unified-guide.md#139-edittable-外部联动配置)
- [实际示例](../examples/edittable-external-linkage-example.php)

## 🔍 搜索关键词

在文档中搜索以下关键词可以快速找到相关内容：

- `externalLinkage`
- `外部联动`
- `edittable 联动`
- `EditTable 外部联动`
- `triggerFields`
- `showWhen`
- `hideWhen`
- `requiredWhen`

## 💡 使用提示

1. **合理选择触发字段**：选择变化频率适中的字段作为触发字段
2. **避免过度复杂**：联动规则保持简洁明了
3. **提供默认值**：为可能被隐藏的字段设置合理默认值
4. **充分测试**：测试各种联动场景确保逻辑正确

## 🐛 问题排查

如果遇到问题，请检查：

1. **字段名称**：确保 `targetFields` 中的字段名与 form 配置一致
2. **操作符**：确认使用了正确的操作符
3. **数据类型**：确保比较值的数据类型正确
4. **控制台日志**：查看浏览器控制台的 `[EditTable外部联动]` 日志

## 🎯 技术实现

- **前端**：Vue 3 provide/inject + watch 监听
- **后端**：配置驱动，无需额外代码
- **性能**：智能防抖，避免频繁计算
- **兼容性**：完全向后兼容，不影响现有功能

---

这个功能让复杂的表单联动变得简单而强大，大大提升了用户体验和开发效率！🎉
